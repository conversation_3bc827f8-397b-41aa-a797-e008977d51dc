<?php

// This script creates a default agency logo image

// Create a blank image
$width = 600;
$height = 300;
$image = imagecreatetruecolor($width, $height);

// Set background color (light gradient)
$bgColor = imagecolorallocate($image, 245, 245, 250);
imagefill($image, 0, 0, $bgColor);

// Add some styling
$accentColor = imagecolorallocate($image, 255, 90, 140); // Pink
$textColor = imagecolorallocate($image, 50, 50, 50); // Dark gray

// Draw a decorative border
imagefilledrectangle($image, 0, 0, $width, 10, $accentColor);
imagefilledrectangle($image, 0, $height - 10, $width, $height, $accentColor);
imagefilledrectangle($image, 0, 0, 10, $height, $accentColor);
imagefilledrectangle($image, $width - 10, 0, $width, $height, $accentColor);

// Add agency name using built-in font
$text = "AGENCY LOGO";
$x = ($width - (strlen($text) * 9)) / 2; // Approximate width of characters
$y = ($height - 30) / 2;
imagestring($image, 5, $x, $y, $text, $textColor);

// Add "ESCORTS" text below
$subText = "ESCORTS & COMPANIONS";
$x = ($width - (strlen($subText) * 7)) / 2;
imagestring($image, 4, $x, $y + 40, $subText, $accentColor);

// Save the image
$outputPath = 'public/images/default-agency-logo.png';
imagepng($image, $outputPath);
imagedestroy($image);

echo "Default agency logo created at $outputPath\n";
