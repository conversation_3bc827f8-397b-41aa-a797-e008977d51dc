<?php

// This script checks agency logo paths in the database

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Agency;

// Get all agencies
$agencies = Agency::all();

// Check logo paths
foreach ($agencies as $agency) {
    echo "Agency: {$agency->name}, Logo Path: " . ($agency->logo_path ?? 'NULL') . "\n";
}

echo "\nTotal agencies: " . $agencies->count() . "\n";
