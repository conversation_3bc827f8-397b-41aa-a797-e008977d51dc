<?php

// Create the directory if it doesn't exist
$dir = 'storage/app/public/agencies';
if (!is_dir($dir)) {
    mkdir($dir, 0755, true);
}

// List of agency names to create logos for
$agencies = [
    'vip-escorts-agency' => 'VIP Escorts',
    'elite-companions' => 'Elite Companions',
    'luxury-escorts' => 'Luxury Escorts',
    'diamond-escorts' => 'Diamond Escorts',
    'elite-models-agency' => 'Elite Models',
    'luxury-escorts-agency' => 'Luxury Escorts',
    'premium-companions' => 'Premium Companions',
    'royal-escorts-agency' => 'Royal Escorts',
    'vip-companions' => 'VIP Companions'
];

// Colors for the logos
$colors = [
    ['bg' => [255, 192, 203], 'text' => [139, 0, 139]], // Pink bg, purple text
    ['bg' => [230, 230, 250], 'text' => [75, 0, 130]],  // Lavender bg, indigo text
    ['bg' => [255, 228, 225], 'text' => [178, 34, 34]], // <PERSON> rose bg, firebrick text
    ['bg' => [240, 248, 255], 'text' => [70, 130, 180]], // Alice blue bg, steel blue text
    ['bg' => [255, 240, 245], 'text' => [199, 21, 133]], // Lavender blush bg, medium violet red text
    ['bg' => [245, 245, 245], 'text' => [219, 112, 147]], // White smoke bg, pale violet red text
    ['bg' => [253, 245, 230], 'text' => [205, 92, 92]],  // Old lace bg, indian red text
    ['bg' => [255, 250, 250], 'text' => [220, 20, 60]],  // Snow bg, crimson text
    ['bg' => [248, 248, 255], 'text' => [106, 90, 205]]  // Ghost white bg, slate blue text
];

// Create a logo for each agency
$i = 0;
foreach ($agencies as $slug => $name) {
    // Create a 400x200 image
    $image = imagecreatetruecolor(400, 200);
    
    // Set background color
    $color = $colors[$i % count($colors)];
    $bg = imagecolorallocate($image, $color['bg'][0], $color['bg'][1], $color['bg'][2]);
    imagefill($image, 0, 0, $bg);
    
    // Set text color
    $textColor = imagecolorallocate($image, $color['text'][0], $color['text'][1], $color['text'][2]);
    
    // Add a border
    $borderColor = imagecolorallocate($image, 200, 200, 200);
    imagerectangle($image, 0, 0, 399, 199, $borderColor);
    
    // Add agency name
    $fontSize = 5;
    $textWidth = imagefontwidth($fontSize) * strlen($name);
    $textHeight = imagefontheight($fontSize);
    $x = (400 - $textWidth) / 2;
    $y = 80;
    imagestring($image, $fontSize, $x, $y, $name, $textColor);
    
    // Add "AGENCY" text
    $agencyText = "AGENCY";
    $agencyTextWidth = imagefontwidth($fontSize) * strlen($agencyText);
    $x = (400 - $agencyTextWidth) / 2;
    $y = 100;
    imagestring($image, $fontSize, $x, $y, $agencyText, $textColor);
    
    // Add a decorative element
    $decorColor = imagecolorallocate($image, $color['text'][0], $color['text'][1], $color['text'][2]);
    imagefilledellipse($image, 200, 50, 60, 30, $decorColor);
    imagefilledellipse($image, 200, 50, 40, 20, $bg);
    
    // Save the image
    $filename = $dir . '/' . $slug . '.png';
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "Created logo for $name: $filename\n";
    $i++;
}

echo "All logos created successfully!\n";
