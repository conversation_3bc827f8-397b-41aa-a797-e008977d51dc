<nav x-data="{ open: false }" class="bg-white border-b border-gray-200 shadow-sm">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}">
                        <x-application-logo class="block h-9 w-auto fill-current text-gray-800" />
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:-my-px sm:ms-10 sm:flex">
                    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                        {{ __('Dashboard') }}
                    </x-nav-link>

                    @auth

                        @if(Auth::user()->user_type == 'escort')
                            <x-nav-link :href="route('profile.escort.edit')" :active="request()->routeIs('profile.escort.edit')">
                                {{ __('My Profile') }}
                            </x-nav-link>
                            <x-nav-link :href="route('escort.photos')" :active="request()->routeIs('escort.photos')">
                                {{ __('Photos') }}
                            </x-nav-link>
                            <x-nav-link :href="route('escort.services')" :active="request()->routeIs('escort.services')">
                                {{ __('Services') }}
                            </x-nav-link>
                            <x-nav-link :href="route('escort.rates')" :active="request()->routeIs('escort.rates')">
                                {{ __('Rates') }}
                            </x-nav-link>
                            <x-nav-link :href="route('escort.locations')" :active="request()->routeIs('escort.locations')">
                                {{ __('Locations') }}
                            </x-nav-link>
                            <x-nav-link :href="route('escort.status-requests.index')" :active="request()->routeIs('escort.status-requests.index')">
                                {{ __('Status Requests') }}
                            </x-nav-link>
                        @endif

                        @if(Auth::user()->user_type == 'agency')
                            <x-nav-link :href="route('agency.escorts')" :active="request()->routeIs('agency.escorts')">
                                {{ __('My Escorts') }}
                            </x-nav-link>
                            <x-nav-link :href="route('agency.escorts.create')" :active="request()->routeIs('agency.escorts.create')">
                                {{ __('Add Escort') }}
                            </x-nav-link>
                            <x-nav-link :href="route('agency.locations')" :active="request()->routeIs('agency.locations')">
                                {{ __('Locations') }}
                            </x-nav-link>
                            <x-nav-link :href="route('agency.status-requests.index')" :active="request()->routeIs('agency.status-requests.index')">
                                {{ __('Status Requests') }}
                            </x-nav-link>
                        @endif

                        @if(Auth::user()->user_type == 'admin')
                            <x-nav-link :href="route('admin.users')" :active="request()->routeIs('admin.users')">
                                {{ __('Users') }}
                            </x-nav-link>
                            <x-nav-link :href="route('admin.escorts')" :active="request()->routeIs('admin.escorts')">
                                {{ __('Escorts') }}
                            </x-nav-link>
                            <x-nav-link :href="route('admin.agencies')" :active="request()->routeIs('admin.agencies')">
                                {{ __('Agencies') }}
                            </x-nav-link>
                            <x-nav-link :href="route('admin.status-requests.index')" :active="request()->routeIs('admin.status-requests.index')">
                                {{ __('Status Requests') }}
                            </x-nav-link>
                            <x-nav-link :href="route('admin.seo.dashboard')" :active="request()->routeIs('admin.seo.*')">
                                {{ __('SEO') }}
                            </x-nav-link>
                        @endif
                    @endauth
                </div>
            </div>

            <!-- Settings Dropdown -->
            <div class="hidden sm:flex sm:items-center sm:ms-6">
                @auth
                    <!-- Notifications -->
                    <a href="{{ route('notifications.index') }}" class="mr-4 relative">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600 hover:text-gray-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                        @php
                            $unreadCount = Auth::user()->unreadNotificationsCount();
                        @endphp
                        @if($unreadCount > 0)
                            <span class="absolute -top-1 -right-1 bg-pink-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">{{ $unreadCount }}</span>
                        @endif
                    </a>

                    <x-dropdown align="right" width="48">
                        <x-slot name="trigger">
                            <button class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none transition ease-in-out duration-150">
                                <div>{{ Auth::user()->username }}</div>

                                <div class="ms-1">
                                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </button>
                        </x-slot>

                        <x-slot name="content">
                            <x-dropdown-link :href="route('profile.edit')">
                                {{ __('Profile') }}
                            </x-dropdown-link>

                            <!-- Authentication -->
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf

                                <x-dropdown-link :href="route('logout')"
                                        onclick="event.preventDefault();
                                                    this.closest('form').submit();">
                                    {{ __('Log Out') }}
                                </x-dropdown-link>
                            </form>
                        </x-slot>
                    </x-dropdown>
                @else
                    <a href="{{ route('login') }}" class="text-sm text-gray-700 hover:text-gray-900 mr-4">{{ __('Login') }}</a>
                    <a href="{{ route('register') }}" class="text-sm text-gray-700 hover:text-gray-900">{{ __('Register') }}</a>
                @endauth
            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-responsive-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                {{ __('Dashboard') }}
            </x-responsive-nav-link>

            @auth
                @if(Auth::user()->user_type == 'escort')
                    <x-responsive-nav-link :href="route('profile.escort.edit')" :active="request()->routeIs('profile.escort.edit')">
                        {{ __('My Profile') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('escort.photos')" :active="request()->routeIs('escort.photos')">
                        {{ __('Photos') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('escort.services')" :active="request()->routeIs('escort.services')">
                        {{ __('Services') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('escort.rates')" :active="request()->routeIs('escort.rates')">
                        {{ __('Rates') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('escort.locations')" :active="request()->routeIs('escort.locations')">
                        {{ __('Locations') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('escort.status-requests.index')" :active="request()->routeIs('escort.status-requests.index')">
                        {{ __('Status Requests') }}
                    </x-responsive-nav-link>
                @endif

                @if(Auth::user()->user_type == 'agency')
                    <x-responsive-nav-link :href="route('agency.escorts')" :active="request()->routeIs('agency.escorts')">
                        {{ __('My Escorts') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('agency.escorts.create')" :active="request()->routeIs('agency.escorts.create')">
                        {{ __('Add Escort') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('agency.locations')" :active="request()->routeIs('agency.locations')">
                        {{ __('Locations') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('agency.status-requests.index')" :active="request()->routeIs('agency.status-requests.index')">
                        {{ __('Status Requests') }}
                    </x-responsive-nav-link>
                @endif

                @if(Auth::user()->user_type == 'admin')
                    <x-responsive-nav-link :href="route('admin.users')" :active="request()->routeIs('admin.users')">
                        {{ __('Users') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('admin.escorts')" :active="request()->routeIs('admin.escorts')">
                        {{ __('Escorts') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('admin.agencies')" :active="request()->routeIs('admin.agencies')">
                        {{ __('Agencies') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('admin.status-requests.index')" :active="request()->routeIs('admin.status-requests.index')">
                        {{ __('Status Requests') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('admin.seo.dashboard')" :active="request()->routeIs('admin.seo.*')">
                        {{ __('SEO') }}
                    </x-responsive-nav-link>
                @endif
            @endauth
        </div>

        <!-- Responsive Settings Options -->
        <div class="pt-4 pb-1 border-t border-gray-200">
            @auth
                <div class="px-4">
                    <div class="font-medium text-base text-gray-800">{{ Auth::user()->username }}</div>
                    <div class="font-medium text-sm text-gray-500">{{ Auth::user()->email }}</div>
                    <div class="font-medium text-sm text-gray-500">{{ ucfirst(Auth::user()->user_type) }}</div>
                </div>

                <div class="mt-3 space-y-1">
                    <x-responsive-nav-link :href="route('profile.edit')">
                        {{ __('Profile') }}
                    </x-responsive-nav-link>

                    <x-responsive-nav-link :href="route('notifications.index')">
                        {{ __('Notifications') }}
                        @php
                            $unreadCount = Auth::user()->unreadNotificationsCount();
                        @endphp
                        @if($unreadCount > 0)
                            <span class="ml-1 bg-pink-600 text-white text-xs font-bold rounded-full h-5 w-5 inline-flex items-center justify-center">{{ $unreadCount }}</span>
                        @endif
                    </x-responsive-nav-link>

                    <!-- Authentication -->
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf

                        <x-responsive-nav-link :href="route('logout')"
                                onclick="event.preventDefault();
                                            this.closest('form').submit();">
                            {{ __('Log Out') }}
                        </x-responsive-nav-link>
                    </form>
                </div>
            @else
                <div class="px-4 py-2 space-y-1">
                    <x-responsive-nav-link :href="route('login')">
                        {{ __('Login') }}
                    </x-responsive-nav-link>
                    <x-responsive-nav-link :href="route('register')">
                        {{ __('Register') }}
                    </x-responsive-nav-link>
                </div>
            @endauth
        </div>
    </div>
</nav>
