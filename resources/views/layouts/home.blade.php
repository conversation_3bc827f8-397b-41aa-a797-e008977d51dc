<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Get Hot Babes') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])


    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.public-navigation')

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Footer -->
            <footer class="bg-gray-900 text-gray-300">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div>
                            <div class="flex items-center mb-4">
                                <!-- Left Icon - Two People Silhouette -->
                                <svg class="w-6 h-6 mr-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7 4c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm10 0c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zM5 9h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1H7v-6H6v6H5c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1zm10 0h4c.6 0 1 .4 1 1v9c0 .6-.4 1-1 1h-1v-6h-1v6h-2c-.6 0-1-.4-1-1v-9c0-.6.4-1 1-1z"/>
                                </svg>

                                <h3 class="text-xl font-bold text-white">{{ $settings['site_name'] ?? 'Get Hot Babes' }}</h3>

                                <!-- Right Icon - Bed -->
                                <svg class="w-6 h-6 ml-3 text-pink-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78-.53-.48-1.23-.78-2-.78H6c-1.65 0-3 1.35-3 3v2.78c-.61.55-1 1.34-1 2.22v6h2v-2h16v2h2v-6c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 4h16v2H4v-2z"/>
                                </svg>
                            </div>
                            <p class="text-gray-400 mb-4">{{ $settings['footer_about'] ?? 'The premier escort directory for high-class companions.' }}</p>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Quick Links</h3>
                            <ul class="space-y-2">
                                <li><a href="{{ route('home') }}" class="text-gray-400 hover:text-pink-500">Home</a></li>
                                <li><a href="{{ route('escorts.index') }}" class="text-gray-400 hover:text-pink-500">Escorts</a></li>
                                <li><a href="{{ route('agencies.index') }}" class="text-gray-400 hover:text-pink-500">Agencies</a></li>
                                <li><a href="{{ route('locations.index') }}" class="text-gray-400 hover:text-pink-500">Locations</a></li>
                                <li><a href="{{ route('contact.index') }}" class="text-gray-400 hover:text-pink-500">Contact</a></li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Popular Locations</h3>
                            <ul class="space-y-2">
                                @php
                                    try {
                                        $footerLocations = \App\Models\Location::active()
                                            ->ofType('city')
                                            ->whereHas('escorts')
                                            ->withCount('escorts')
                                            ->orderBy('escorts_count', 'desc')
                                            ->take(5)
                                            ->get();
                                    } catch (\Exception $e) {
                                        $footerLocations = collect([
                                            (object)['name' => 'Kampala', 'slug' => 'kampala'],
                                            (object)['name' => 'Entebbe', 'slug' => 'entebbe'],
                                            (object)['name' => 'Jinja', 'slug' => 'jinja'],
                                            (object)['name' => 'Mbarara', 'slug' => 'mbarara'],
                                            (object)['name' => 'Gulu', 'slug' => 'gulu'],
                                        ]);
                                    }
                                @endphp
                                @foreach($footerLocations as $location)
                                    <li><a href="{{ route('locations.show', $location->slug) }}" class="text-gray-400 hover:text-pink-500 transition-colors duration-300">{{ $location->name }}</a></li>
                                @endforeach
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold text-white mb-4">Contact Us</h3>
                            <p class="text-gray-400 mb-2">Have questions or need assistance?</p>
                            <a href="{{ route('contact.index') }}" class="text-pink-500 hover:text-white">Contact Us</a>
                            <p class="text-gray-400 mt-4 mb-2">Join as an escort or agency:</p>
                            <a href="{{ route('register') }}" class="inline-block bg-pink-500 hover:bg-pink-600 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300">
                                Register Now
                            </a>
                        </div>
                    </div>

                    <div class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm">
                        <p>&copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Get Hot Babes' }}. All rights reserved.</p>
                        <div class="mt-2 space-x-4">
                            <a href="#" class="hover:text-pink-500">Terms of Service</a>
                            <a href="#" class="hover:text-pink-500">Privacy Policy</a>
                            <a href="#" class="hover:text-pink-500">Cookie Policy</a>
                        </div>
                        <p class="mt-4">
                            {{ $settings['footer_text'] ?? 'This website contains adult content and is only suitable for those who are 18 years or older.' }}
                        </p>
                    </div>
                </div>
            </footer>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />
        </div>
        <!-- Age Disclaimer Modal -->
        <x-age-disclaimer />
    </body>
</html>
