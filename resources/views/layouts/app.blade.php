<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Get Hot Babes') }}</title>

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Font Awesome Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])


    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Admin Notifications -->
            @auth
                @if(auth()->user()->notifications()->unread()->where('type', 'admin_action')->orWhere('type', 'announcement')->count() > 0)
                    <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                        <div class="space-y-2">
                            @foreach(auth()->user()->notifications()->unread()->where(function($query) {
                                $query->where('type', 'admin_action')->orWhere('type', 'announcement');
                            })->latest()->take(3)->get() as $notification)
                                <x-notification-alert :notification="$notification" />
                            @endforeach

                            @if(auth()->user()->notifications()->unread()->where(function($query) {
                                $query->where('type', 'admin_action')->orWhere('type', 'announcement');
                            })->count() > 3)
                                <div class="text-center py-2">
                                    <a href="{{ route('notifications.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">View all notifications</a>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            @endauth

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />
        </div>

        <!-- Age Disclaimer Modal -->
        <x-age-disclaimer />
    </body>
</html>
