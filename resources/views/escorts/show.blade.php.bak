<x-public-layout>
    <style>
        .text-xxs {
            font-size: 0.65rem;
            line-height: 1rem;
        }

        /* Watermark */
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            font-size: 36px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.3);
            pointer-events: none;
            z-index: 5;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
        }

        /* Media indicators */
        .media-indicators {
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            gap: 5px;
            z-index: 10;
        }

        .media-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            font-size: 14px;
        }

        /* Tab styling */
        .tab-button {
            position: relative;
            transition: all 0.3s;
        }

        .tab-button.active {
            color: #ec4899;
            border-bottom: 2px solid #ec4899;
        }

        .tab-button:not(.active) {
            color: #6b7280;
        }

        .tab-button:hover:not(.active) {
            color: #374151;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
    <!-- Hero Section with Escort Name -->
    <div class="relative bg-gradient-to-r from-gray-900 to-pink-900 text-white py-16 overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-60"></div>
        <div class="absolute inset-0 bg-pattern opacity-10" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl md:text-5xl font-bold text-white drop-shadow-md">{{ $escort->name }}</h1>
                    <div class="flex flex-wrap items-center mt-4 text-gray-200 text-sm md:text-base">
                        @if($escort->locations->count() > 0)
                            <div class="flex items-center mr-6 mb-2 md:mb-0">
                                <svg class="w-5 h-5 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $escort->locations->first()->name }}
                            </div>
                        @endif
                        <div class="flex items-center mr-6 mb-2 md:mb-0">
                            <svg class="w-5 h-5 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {{ $escort->age }} years
                        </div>
                        <div class="flex items-center mr-6 mb-2 md:mb-0">
                            <svg class="w-5 h-5 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            {{ $escort->profile_views }} views
                        </div>
                        <div class="flex items-center mb-2 md:mb-0">
                            <svg class="w-5 h-5 mr-2 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Last seen recently
                        </div>
                    </div>
                </div>
                <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
                    <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-colors duration-300" title="Add to favorites">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </button>
                    <button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-colors duration-300" title="Share profile">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- E-commerce Style Product Detail Layout -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="flex flex-col md:flex-row">
                    <!-- Left Column: Media Gallery (1/2 width on desktop) -->
                    <div class="md:w-1/2 p-6">
                        @if($escort->images->count() > 0)
                            @php
                                $mainImage = $escort->images->where('is_primary', true)->first() ?? $escort->images->first();
                            @endphp

                            <!-- Main Image Display -->
                            <div class="relative rounded-lg overflow-hidden mb-4 border border-gray-200">
                                <div class="aspect-w-4 aspect-h-5 bg-gray-100">
                                    <img
                                        src="{{ asset('storage/' . $mainImage->image_path) }}"
                                        alt="{{ $escort->name }}"
                                        class="object-cover w-full h-full"
                                        id="main-media"
                                    >
                                    <!-- Watermark -->
                                    <div class="watermark">GHB</div>

                                    <!-- Media Type Indicator (for videos) -->
                                    @if($escort->id % 3 == 0 && $mainImage->id % 3 == 0)
                                        <div class="absolute bottom-4 right-4 bg-black bg-opacity-70 rounded-full p-2">
                                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Thumbnail Selector -->
                            <div class="grid grid-cols-5 gap-2">
                                @foreach($escort->images->take(5) as $index => $image)
                                    <div
                                        class="media-thumbnail cursor-pointer rounded-md overflow-hidden border {{ $mainImage && $mainImage->id == $image->id ? 'border-pink' : 'border-gray-200' }} hover:border-pink transition-colors duration-200"
                                        data-media-path="{{ asset('storage/' . $image->image_path) }}"
                                        data-media-type="image"
                                        data-media-id="{{ $image->id }}"
                                    >
                                        <div class="aspect-w-1 aspect-h-1 bg-gray-100">
                                            <img
                                                src="{{ asset('storage/' . $image->image_path) }}"
                                                alt="{{ $escort->name }} - Image {{ $index + 1 }}"
                                                class="object-cover w-full h-full"
                                            >

                                            <!-- Video Indicator -->
                                            @if($escort->id % 3 == 0 && $image->id % 3 == 0)
                                                <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- View All Media Link -->
                            @if($escort->images->count() > 5)
                                <div class="mt-3 text-center">
                                    <a href="#gallery" class="text-pink hover:text-pink-700 text-sm font-medium inline-flex items-center transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        View All {{ $escort->images->count() }} Photos
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="w-full aspect-w-4 aspect-h-5 bg-gray-200 flex items-center justify-center rounded-lg">
                                <span class="text-gray-400">No images available</span>
                            </div>
                        @endif
                    </div>

                    <!-- Right Column: Escort Details (1/2 width on desktop) -->
                    <div class="md:w-1/2 p-6 md:border-l border-gray-200">
                        <!-- Status Badges -->
                        <div class="flex flex-wrap gap-2 mb-4">
                            @if($escort->is_verified)
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Verified
                                </span>
                            @endif
                            @if($escort->is_featured)
                                <span class="bg-pink-100 text-pink-800 text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    Featured
                                </span>
                            @endif
                            @if($escort->is_new)
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    New
                                </span>
                            @endif

                            <!-- Media Indicators -->
                            <div class="ml-auto flex gap-2">
                                <span class="text-gray-600 text-xs flex items-center">
                                    <svg class="w-3 h-3 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $escort->images->count() }}
                                </span>
                                @if($escort->id % 3 == 0)
                                    <span class="text-gray-600 text-xs flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                        </svg>
                                        2
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Escort Name and Basic Info -->
                        <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $escort->name }}</h1>

                        <div class="flex items-center mb-4">
                            @if($escort->locations->count() > 0)
                                <div class="flex items-center text-gray-600 mr-4">
                                    <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $escort->locations->first()->name }}
                                </div>
                            @endif
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                {{ $escort->age }} years
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Height</p>
                                <p class="text-gray-800 font-medium">{{ $escort->height_cm }} cm</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Build</p>
                                <p class="text-gray-800 font-medium">{{ $escort->build }}</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Hair</p>
                                <p class="text-gray-800 font-medium">{{ $escort->hair_color }}</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Ethnicity</p>
                                <p class="text-gray-800 font-medium">{{ $escort->ethnicity }}</p>
                            </div>
                        </div>

                        <!-- Availability -->
                        <div class="mb-6">
                            <h2 class="text-lg font-semibold text-gray-800 mb-3">Availability</h2>
                            <div class="flex space-x-4">
                                <div class="flex-1 bg-gray-50 p-3 rounded-md text-center {{ $escort->incall_available ? 'border-l-4 border-green-500' : 'border-l-4 border-gray-300' }}">
                                    <p class="text-gray-500 text-xs mb-1">Incall</p>
                                    <p class="text-gray-800 font-medium">{{ $escort->incall_available ? 'Available' : 'Not Available' }}</p>
                                </div>
                                <div class="flex-1 bg-gray-50 p-3 rounded-md text-center {{ $escort->outcall_available ? 'border-l-4 border-green-500' : 'border-l-4 border-gray-300' }}">
                                    <p class="text-gray-500 text-xs mb-1">Outcall</p>
                                    <p class="text-gray-800 font-medium">{{ $escort->outcall_available ? 'Available' : 'Not Available' }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Button -->
                        <div class="mt-6">
                            <a href="#contact" class="block w-full bg-pink hover:bg-pink-600 text-white font-medium py-3 px-4 rounded-md transition-colors duration-300 text-center shadow-sm">
                                Contact {{ $escort->name }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JavaScript for Media Interaction -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const mainMedia = document.getElementById('main-media');
                    const thumbnails = document.querySelectorAll('.media-thumbnail');

                    thumbnails.forEach(thumbnail => {
                        thumbnail.addEventListener('click', function() {
                            // Remove highlight from all thumbnails
                            thumbnails.forEach(t => t.classList.remove('border-pink'));
                            thumbnails.forEach(t => t.classList.add('border-gray-200'));

                            // Add highlight to clicked thumbnail
                            this.classList.remove('border-gray-200');
                            this.classList.add('border-pink');

                            const mediaType = this.getAttribute('data-media-type');

                            if (mediaType === 'image') {
                                const mediaPath = this.getAttribute('data-media-path');
                                mainMedia.src = mediaPath;
                            } else if (mediaType === 'video') {
                                // For demonstration - in a real implementation, this would load a video player
                                alert('Video playback would be implemented here');
                            }
                        });
                    });
                });
            </script>

            <!-- Tabs Section -->
            <div class="mt-8 bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Tabs -->
                <div class="border-b border-gray-200">
                    <div class="flex -mb-px">
                        <button id="tab-about" class="tab-button px-6 py-3 font-medium active" onclick="switchTab('about')">About</button>
                        <button id="tab-services" class="tab-button px-6 py-3 font-medium" onclick="switchTab('services')">Services</button>
                        <button id="tab-rates" class="tab-button px-6 py-3 font-medium" onclick="switchTab('rates')">Rates</button>
                        <button id="tab-reviews" class="tab-button px-6 py-3 font-medium" onclick="switchTab('reviews')">Reviews</button>
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- About Content -->
                    <div id="content-about" class="tab-content active">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">About {{ $escort->name }}</h2>

                        @if($escort->about)
                            <div class="prose max-w-none mb-6">
                                <p class="text-gray-700">{{ $escort->about }}</p>
                            </div>
                        @else
                            <p class="text-gray-500 mb-6">No description provided.</p>
                        @endif

                        <!-- Languages -->
                        @if($escort->languages->count() > 0)
                            <h3 class="text-lg font-semibold text-gray-800 mb-3">Languages</h3>
                            <div class="flex flex-wrap gap-2 mb-6">
                                @foreach($escort->languages as $language)
                                    <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                        {{ $language->name }} ({{ ucfirst($language->pivot->proficiency) }})
                                    </span>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Services Content -->
                    <div id="content-services" class="tab-content">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Services Offered</h2>

                        @if($escort->services->count() > 0)
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                @foreach($escort->services as $service)
                                    <div class="flex items-center p-3 bg-gray-50 rounded-md">
                                        <svg class="w-5 h-5 text-pink mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-800">{{ $service->name }}</span>
                                    </div>
                                @endforeach
                            </div>
                            <p class="text-sm text-gray-500 italic">Please contact for detailed information about specific services and rates.</p>
                        @else
                            <p class="text-gray-500 mb-6">No services listed. Please contact for more information.</p>
                        @endif
                    </div>

                    <!-- Rates Content -->
                    <div id="content-rates" class="tab-content">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Rates</h2>

                        @if($escort->rates->count() > 0)
                            <div class="overflow-hidden rounded-lg border border-gray-200 mb-6">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                            @if($escort->incall_available)
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Incall</th>
                                            @endif
                                            @if($escort->outcall_available)
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outcall</th>
                                            @endif
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($escort->rates as $rate)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $rate->duration }}</td>
                                                @if($escort->incall_available)
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{ $rate->incall_price ?? 'N/A' }}</td>
                                                @endif
                                                @if($escort->outcall_available)
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{ $rate->outcall_price ?? 'N/A' }}</td>
                                                @endif
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <p class="text-sm text-gray-500 italic">Rates are subject to change. Please confirm when booking.</p>
                        @else
                            <p class="text-gray-500 mb-6">No rates listed. Please contact for pricing information.</p>
                        @endif
                    </div>

                    <!-- Reviews Content -->
                    <div id="content-reviews" class="tab-content">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Client Reviews</h2>

                        <div class="bg-gray-50 rounded-lg p-6 text-center">
                            <p class="text-gray-500 mb-4">No reviews yet. Be the first to leave a review!</p>
                            <button class="bg-pink hover:bg-pink-600 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300 shadow-sm inline-flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Write a Review
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Similar Escorts Section -->
            @if($similarEscorts->count() > 0)
                <div class="mt-8 mb-12">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">Similar Escorts You May Like</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($similarEscorts->take(4) as $similar)
                            <div class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                                <a href="{{ route('escorts.show', $similar->slug) }}" class="block">
                                    @if($similar->primaryImage)
                                        <div class="aspect-w-1 aspect-h-1 bg-gray-100 relative">
                                            <img src="{{ asset('storage/' . $similar->primaryImage->image_path) }}" alt="{{ $similar->name }}" class="object-cover w-full h-full">
                                            <div class="watermark text-xs">GHB</div>
                                        </div>
                                    @else
                                        <div class="aspect-w-1 aspect-h-1 bg-gray-200 flex items-center justify-center">
                                            <span class="text-gray-400 text-sm">No image</span>
                                        </div>
                                    @endif
                                </a>

                                <div class="p-4">
                                    <div class="flex justify-between items-center mb-1">
                                        <h3 class="text-lg font-semibold text-gray-800">{{ $similar->name }}</h3>
                                        <span class="text-sm text-gray-500">{{ $similar->age }} yrs</span>
                                    </div>

                                    @if($similar->locations->count() > 0)
                                        <div class="text-sm text-gray-500 mb-3 flex items-center">
                                            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $similar->locations->first()->name }}
                                        </div>
                                    @endif

                                    <a href="{{ route('escorts.show', $similar->slug) }}" class="mt-2 text-pink hover:text-pink-600 text-sm font-medium inline-flex items-center transition-colors duration-200">
                                        View Profile
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

                                <!-- Quick Info Grid -->
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                                    <div class="bg-gray-50 p-3 rounded">
                                        <p class="text-gray-500 text-sm">Gender</p>
                                        <p class="text-gray-800 font-medium">{{ ucfirst($escort->gender) }}</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded">
                                        <p class="text-gray-500 text-sm">Ethnicity</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->ethnicity }}</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded">
                                        <p class="text-gray-500 text-sm">Height</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->height_cm }} cm</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded">
                                        <p class="text-gray-500 text-sm">Build</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->build }}</p>
                                    </div>
                                    <div class="bg-gray-50 p-3 rounded">
                                        <p class="text-gray-500 text-sm">Hair</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->hair_color }}, {{ $escort->hair_length }}</p>
                                    </div>
                                    @if($escort->bust_size)
                                        <div class="bg-gray-50 p-3 rounded">
                                            <p class="text-gray-500 text-sm">Bust Size</p>
                                            <p class="text-gray-800 font-medium">{{ $escort->bust_size }}</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- Services -->
                                @if($escort->services->count() > 0)
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Services Offered</h3>
                                    <div class="flex flex-wrap gap-2 mb-6">
                                        @foreach($escort->services as $service)
                                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                                {{ $service->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                @endif

                                <!-- Languages -->
                                @if($escort->languages->count() > 0)
                                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Languages</h3>
                                    <div class="flex flex-wrap gap-2 mb-6">
                                        @foreach($escort->languages as $language)
                                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                                {{ $language->name }} ({{ ucfirst($language->pivot->proficiency) }})
                                            </span>
                                        @endforeach
                                    </div>
                                @endif
                            </div>

                            <!-- Services Content -->
                            <div id="content-services" class="tab-content">
                                <h2 class="text-xl font-bold text-gray-800 mb-4">Services Offered</h2>

                                @if($escort->services->count() > 0)
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                        @foreach($escort->services as $service)
                                            <div class="flex items-center p-3 bg-gray-50 rounded">
                                                <svg class="w-5 h-5 text-pink mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="text-gray-800">{{ $service->name }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                    <p class="text-sm text-gray-500 italic">Please contact for detailed information about specific services and rates.</p>
                                @else
                                    <p class="text-gray-500 mb-6">No services listed. Please contact for more information.</p>
                                @endif

                                <h3 class="text-lg font-semibold text-gray-800 mt-8 mb-3">Availability</h3>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                    <div class="flex items-center p-3 bg-gray-50 rounded">
                                        <span class="text-gray-500 mr-2">Incall:</span>
                                        <span class="text-gray-800 font-medium">{{ $escort->incall_available ? 'Available' : 'Not Available' }}</span>
                                    </div>
                                    <div class="flex items-center p-3 bg-gray-50 rounded">
                                        <span class="text-gray-500 mr-2">Outcall:</span>
                                        <span class="text-gray-800 font-medium">{{ $escort->outcall_available ? 'Available' : 'Not Available' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Reviews Content -->
                            <div id="content-reviews" class="tab-content">
                                <h2 class="text-xl font-bold text-gray-800 mb-4">Client Reviews</h2>

                                <div class="bg-gray-50 rounded-lg p-6 text-center">
                                    <p class="text-gray-500 mb-4">No reviews yet. Be the first to leave a review!</p>
                                    <button class="bg-pink hover:bg-pink-600 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300 shadow-sm inline-flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Write a Review
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Right Column: Rates and Contact -->
                <div class="lg:w-1/3">
                    <!-- Rates Card -->
                    @if($escort->rates->count() > 0)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                            <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-4 px-6">
                                <h2 class="text-xl font-bold text-white">Rates</h2>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-3 gap-2 text-sm font-medium text-gray-500 mb-2">
                                    <div>Duration</div>
                                    @if($escort->incall_available)
                                        <div>Incall</div>
                                    @endif
                                    @if($escort->outcall_available)
                                        <div>Outcall</div>
                                    @endif
                                </div>

                                @foreach($escort->rates as $rate)
                                    <div class="grid grid-cols-3 gap-2 py-3 border-t border-gray-200">
                                        <div class="text-gray-800 font-medium">{{ $rate->duration }}</div>
                                        @if($escort->incall_available)
                                            <div class="text-gray-800 font-medium">${{ $rate->incall_price ?? 'N/A' }}</div>
                                        @endif
                                        @if($escort->outcall_available)
                                            <div class="text-gray-800 font-medium">${{ $rate->outcall_price ?? 'N/A' }}</div>
                                        @endif
                                    </div>
                                @endforeach

                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex items-center mb-2">
                                        @if($escort->incall_available)
                                            <div class="flex items-center mr-4">
                                                <svg class="w-5 h-5 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="text-gray-700">Incall</span>
                                            </div>
                                        @endif
                                        @if($escort->outcall_available)
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="text-gray-700">Outcall</span>
                                            </div>
                                        @endif
                                    </div>
                                    <p class="text-sm text-gray-500">Rates are subject to change. Please confirm when booking.</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Contact Card -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                        <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-4 px-6">
                            <h2 class="text-xl font-bold text-white">Contact {{ $escort->name }}</h2>
                        </div>
                        <div class="p-6">
                            <form>
                                <div class="mb-4">
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                                    <input type="text" id="name" name="name" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink">
                                </div>

                                <div class="mb-4">
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Your Email</label>
                                    <input type="email" id="email" name="email" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink">
                                </div>

                                <div class="mb-4">
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Your Phone</label>
                                    <input type="tel" id="phone" name="phone" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink">
                                </div>

                                <div class="mb-4">
                                    <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Preferred Date</label>
                                    <input type="date" id="date" name="date" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink">
                                </div>

                                <div class="mb-4">
                                    <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Your Message</label>
                                    <textarea id="message" name="message" rows="4" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink"></textarea>
                                </div>

                                <button type="submit" class="w-full bg-pink hover:bg-pink-600 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-300 shadow-md">
                                    Send Message
                                </button>
                                <p class="mt-2 text-xs text-gray-500 text-center">
                                    By contacting, you confirm you are over 18 years old.
                                </p>
                            </form>
                        </div>
                    </div>

                    <!-- Additional Details Card -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                        <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-4 px-6">
                            <h2 class="text-xl font-bold text-white">Additional Details</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                @if($escort->sexual_orientation)
                                    <div class="flex justify-between">
                                        <p class="text-gray-500">Sexual Orientation</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->sexual_orientation }}</p>
                                    </div>
                                @endif

                                @if($escort->zodiac_sign)
                                    <div class="flex justify-between">
                                        <p class="text-gray-500">Zodiac Sign</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->zodiac_sign }}</p>
                                    </div>
                                @endif

                                @if($escort->education)
                                    <div class="flex justify-between">
                                        <p class="text-gray-500">Education</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->education }}</p>
                                    </div>
                                @endif

                                @if($escort->occupation)
                                    <div class="flex justify-between">
                                        <p class="text-gray-500">Occupation</p>
                                        <p class="text-gray-800 font-medium">{{ $escort->occupation }}</p>
                                    </div>
                                @endif

                                <div class="flex justify-between">
                                    <p class="text-gray-500">Smoker</p>
                                    <p class="text-gray-800 font-medium">{{ $escort->smoker ? 'Yes' : 'No' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Full Gallery Section -->
            @if($escort->images->count() > 1)
                <div id="gallery" class="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-4 px-6">
                        <h2 class="text-xl font-bold text-white">Photo Gallery</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                            @foreach($escort->images as $image)
                                <a href="{{ asset('storage/' . $image->image_path) }}" class="block">
                                    <div class="w-full h-48 overflow-hidden rounded shadow-md bg-gray-100">
                                        <img src="{{ asset('storage/' . $image->image_path) }}" alt="{{ $escort->name }}" class="w-full h-48 object-cover object-center hover:opacity-80 transition-opacity duration-300" style="object-fit: cover; aspect-ratio: 3/4;">
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Similar Escorts Section -->
            @if($similarEscorts->count() > 0)
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Similar Escorts</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($similarEscorts as $similar)
                            <div class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 escort-card">
                                <a href="{{ route('escorts.show', $similar->slug) }}" class="block relative overflow-hidden">
                                    @if($similar->primaryImage)
                                        <div class="w-full h-64 overflow-hidden bg-gray-100 relative">
                                            <img src="{{ asset('storage/' . $similar->primaryImage->image_path) }}" alt="{{ $similar->name }}" class="w-full h-64 object-cover object-center transform hover:scale-105 transition-transform duration-500" style="object-fit: cover; aspect-ratio: 3/4;">

                                            <!-- Watermark -->
                                            <div class="watermark">GHB</div>

                                            <!-- Media indicators -->
                                            <div class="media-indicators">
                                                @if($similar->images->count() > 1)
                                                    <div class="media-indicator" title="{{ $similar->images->count() }} Photos">
                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                @endif
                                                <!-- For demonstration purposes, show video icon for escorts with ID divisible by 3 -->
                                                @if($similar->id % 3 == 0)
                                                    <div class="media-indicator" title="Has Videos">
                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                                        </svg>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @else
                                        <div class="w-full h-64 bg-gray-200 flex items-center justify-center">
                                            <span class="text-gray-400">No image available</span>
                                        </div>
                                    @endif

                                    <!-- Badges -->
                                    <div class="absolute top-2 right-2 flex flex-col gap-1 z-10">
                                        @if($similar->is_verified)
                                            <span class="bg-green-500 bg-opacity-80 text-white text-xs px-2 py-1 rounded shadow-sm flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                Verified
                                            </span>
                                        @endif
                                        @if($similar->is_new)
                                            <span class="bg-orange-500 bg-opacity-80 text-white text-xs px-2 py-1 rounded shadow-sm flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                New
                                            </span>
                                        @endif
                                        @if($similar->is_featured)
                                            <span class="bg-pink bg-opacity-80 text-white text-xs px-2 py-1 rounded shadow-sm flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                </svg>
                                                Featured
                                            </span>
                                        @endif
                                    </div>
                                </a>

                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">{{ $similar->name }}</h3>
                                        <span class="text-sm text-gray-500">{{ $similar->age }} yrs</span>
                                    </div>

                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                        @if($similar->locations->count() > 0)
                                            <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $similar->locations->first()->name }}
                                        @endif
                                    </div>

                                    <div class="mt-3 pt-3 border-t border-gray-200 text-right">
                                        <a href="{{ route('escorts.show', $similar->slug) }}" class="bg-pink hover:bg-pink-600 text-white text-sm font-medium px-3 py-1 rounded transition-colors duration-300 shadow-sm">
                                            View Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // Hide all tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Deactivate all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Activate selected tab and content
            document.getElementById('content-' + tabName).classList.add('active');
            document.getElementById('tab-' + tabName).classList.add('active');
        }
    </script>
</x-public-layout>
