<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Test Confirmation Modal') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">
                        {{ __('Test Buttons') }}
                    </h2>

                    <div class="space-y-4">
                        <!-- Test Button 1: Simple data-confirm -->
                        <div class="p-4 border border-gray-200 rounded-md">
                            <h3 class="font-medium mb-2">Test 1: Simple data-confirm</h3>
                            <form action="#" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md" data-confirm="Are you sure you want to perform this action?">
                                    Delete Item
                                </button>
                            </form>
                        </div>

                        <!-- Test Button 2: JavaScript API -->
                        <div class="p-4 border border-gray-200 rounded-md">
                            <h3 class="font-medium mb-2">Test 2: JavaScript API</h3>
                            <button type="button" class="bg-blue-600 text-white px-4 py-2 rounded-md" onclick="confirmAction({
                                message: 'This is a custom message from JavaScript',
                                formAction: '#'
                            })">
                                Custom Confirmation
                            </button>
                        </div>

                        <!-- Test Button 3: Custom Modal ID -->
                        <div class="p-4 border border-gray-200 rounded-md">
                            <h3 class="font-medium mb-2">Test 3: Custom Modal ID</h3>
                            <form action="#" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md" 
                                    data-confirm="This uses a custom modal ID" 
                                    data-modal-id="custom-modal">
                                    Custom Modal ID
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Modal -->
    <x-confirmation-modal id="custom-modal" title="Custom Modal" />
</x-app-layout>
