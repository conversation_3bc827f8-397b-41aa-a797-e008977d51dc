<x-home-layout>
    <!-- Hero Section -->
    <section class="relative bg-gray-900 text-white">
        <!-- Hero Background Image -->
        <div class="absolute inset-0 overflow-hidden">
            <img src="https://images.unsplash.com/photo-1517263904808-5dc91e3e7044?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1976&q=80" alt="Luxury background" class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900/80 to-gray-900/90"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 lg:py-40">
            <div class="max-w-3xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl mb-6">
                    <span class="text-pink">{{ $settings['site_tagline'] ?? 'Discover' }}</span> Exclusive Companions
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-10">
                    {{ $settings['site_description'] ?? 'Connect with sophisticated escorts and premium agencies for unforgettable experiences.' }}
                </p>
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                    <a href="{{ route('escorts.index') }}" class="btn-primary">
                        Browse Escorts
                    </a>
                    <a href="{{ route('locations.index') }}" class="btn-dark">
                        Find by Location
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="bg-gray-800 py-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="{{ route('escorts.search') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-300 mb-1">Location</label>
                    <select id="location" name="location" class="form-input">
                        <option value="">All Locations</option>
                        <option value="london">London</option>
                        <option value="manchester">Manchester</option>
                        <option value="birmingham">Birmingham</option>
                        <option value="glasgow">Glasgow</option>
                    </select>
                </div>
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-300 mb-1">Category</label>
                    <select id="category" name="category" class="form-input">
                        <option value="">All Categories</option>
                        <option value="escort">Escorts</option>
                        <option value="agency">Agencies</option>
                    </select>
                </div>
                <div>
                    <label for="service" class="block text-sm font-medium text-gray-300 mb-1">Service</label>
                    <select id="service" name="service" class="form-input">
                        <option value="">All Services</option>
                        <option value="incall">Incall</option>
                        <option value="outcall">Outcall</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full btn-primary py-2 px-4">
                        Search
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Featured Escorts Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="section-title">Featured <span class="text-pink">Escorts</span></h2>
                <p class="section-description">
                    Discover our selection of premium companions available for booking.
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @forelse($featuredEscorts as $escort)
                    <div class="card group">
                        <div class="relative pb-[130%]">
                            @if($escort->primaryImage)
                                <img src="{{ asset('storage/' . $escort->primaryImage->image_path) }}" alt="{{ $escort->name }}" class="absolute inset-0 w-full h-full object-cover group-hover:scale-105 transition-transform duration-500">
                            @else
                                <div class="absolute inset-0 bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-400">No Image</span>
                                </div>
                            @endif

                            @if($escort->is_new)
                                <div class="absolute top-3 right-3 bg-pink text-white text-xs font-bold px-2 py-1 rounded-sm">
                                    NEW
                                </div>
                            @endif

                            <div class="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black to-transparent pt-20 pb-3 sm:pb-4 px-3 sm:px-4">
                                <h3 class="text-lg sm:text-xl font-bold text-white truncate">{{ $escort->name }}</h3>
                                <div class="flex items-center text-xs sm:text-sm text-gray-300 mt-1">
                                    <span class="truncate">{{ $escort->age }} • {{ optional($escort->primaryLocation())->name ?? 'Unknown' }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 sm:p-4">
                            <div class="flex justify-end items-center mb-2">
                                <div class="flex space-x-1 sm:space-x-2">
                                    @if($escort->incall_available)
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-800 rounded-sm">Incall</span>
                                    @endif
                                    @if($escort->outcall_available)
                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-800 rounded-sm">Outcall</span>
                                    @endif
                                </div>
                            </div>
                            <a href="{{ route('escorts.show', $escort->slug) }}" class="mt-2 block w-full text-center py-1.5 sm:py-2 border border-pink text-pink font-medium rounded hover:bg-pink hover:text-white transition-colors duration-300 text-sm sm:text-base">
                                View Profile
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="col-span-4 text-center py-8">
                        <p class="text-gray-500">No featured escorts available at the moment.</p>
                    </div>
                @endforelse
            </div>

            <div class="mt-12 text-center">
                <a href="{{ route('escorts.index') }}" class="btn-secondary">
                    View All Escorts
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Popular Locations Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="section-title">Popular <span class="text-pink">Locations</span></h2>
                <p class="section-description">
                    Find companions in these popular cities across the UK.
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @forelse($popularLocations as $location)
                    <a href="{{ route('locations.show', $location->slug) }}" class="group relative rounded-lg overflow-hidden bg-white shadow-md hover:shadow-lg">
                        <div class="relative pb-[75%]">
                            <!-- Use a default image for now, ideally you would have location images in the database -->
                            <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent"></div>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 p-3 sm:p-4">
                            <h3 class="text-lg sm:text-xl font-bold text-white truncate">{{ $location->name }}</h3>
                            <div class="flex items-center justify-between">
                                <p class="text-xs sm:text-sm text-gray-200">{{ $location->escorts_count }}+ escorts</p>
                                <span class="text-pink text-xs sm:text-sm font-medium">View →</span>
                            </div>
                        </div>
                    </a>
                @empty
                    <div class="col-span-4 text-center py-8">
                        <p class="text-gray-500">No popular locations available at the moment.</p>
                    </div>
                @endforelse
            </div>

            <div class="mt-12 text-center">
                <a href="{{ route('locations.index') }}" class="btn-secondary">
                    View All Locations
                    <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="section-title">Why Choose <span class="text-pink">Us</span></h2>
                <p class="section-description">
                    We provide a premium platform with exceptional features for escorts and agencies.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 sm:p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                    <div class="w-14 h-14 bg-pink bg-opacity-10 text-pink rounded-full flex items-center justify-center mb-6">
                        <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Verified Profiles</h3>
                    <p class="text-gray-600">
                        All escorts and agencies on our platform are thoroughly verified to ensure authenticity and quality service.
                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white p-6 sm:p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                    <div class="w-14 h-14 bg-pink bg-opacity-10 text-pink rounded-full flex items-center justify-center mb-6">
                        <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Privacy & Discretion</h3>
                    <p class="text-gray-600">
                        Your privacy is our priority. We ensure discreet interactions and secure communication for all escorts and agencies using our platform.
                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white p-6 sm:p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                    <div class="w-14 h-14 bg-pink bg-opacity-10 text-pink rounded-full flex items-center justify-center mb-6">
                        <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Premium Selection</h3>
                    <p class="text-gray-600">
                        We showcase a curated selection of high-class escorts and reputable agencies across the UK, ensuring maximum visibility for your services.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">{{ $settings['cta_title'] ?? 'Are You an' }} <span class="text-pink">{{ $settings['cta_highlight'] ?? 'Escort or Agency' }}</span>?</h2>
            <p class="max-w-2xl mx-auto text-xl text-gray-300 mb-10">
                {{ $settings['cta_description'] ?? 'Join our platform today to showcase your services and connect with potential clients.' }}
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <a href="{{ route('register') }}" class="btn-primary">
                    {{ $settings['cta_button_primary'] ?? 'Register as Escort/Agency' }}
                </a>
                <a href="{{ route('contact.index') }}" class="btn-dark">
                    {{ $settings['cta_button_secondary'] ?? 'Contact Us' }}
                </a>
            </div>
        </div>
    </section>
</x-home-layout>
