<x-admin-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Sitemap Management') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.seo.dashboard') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Dashboard
                </a>
                <form action="{{ route('admin.seo.regenerate-sitemaps') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Regenerate All Sitemaps
                    </button>
                </form>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Main Sitemap -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Main Sitemap</h3>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            {{ $sitemapStats['main_sitemap']['status'] }}
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">URL</p>
                            <a href="{{ $sitemapStats['main_sitemap']['url'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                {{ $sitemapStats['main_sitemap']['url'] }}
                            </a>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Last Generated</p>
                            <p class="text-sm font-medium">{{ $sitemapStats['main_sitemap']['last_generated'] }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Status</p>
                            <p class="text-sm font-medium text-green-600">Active</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sub-Sitemaps -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Escorts Sitemap -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Escorts Sitemap</h3>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                {{ number_format($sitemapStats['escorts_sitemap']['count']) }} URLs
                            </span>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-600">URL</p>
                                <a href="{{ $sitemapStats['escorts_sitemap']['url'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium break-all">
                                    {{ $sitemapStats['escorts_sitemap']['url'] }}
                                </a>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Escorts</p>
                                <p class="text-sm font-medium">{{ number_format($sitemapStats['escorts_sitemap']['count']) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Last Updated</p>
                                <p class="text-sm font-medium">
                                    {{ $sitemapStats['escorts_sitemap']['last_updated'] ? $sitemapStats['escorts_sitemap']['last_updated']->diffForHumans() : 'Never' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agencies Sitemap -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Agencies Sitemap</h3>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                                {{ number_format($sitemapStats['agencies_sitemap']['count']) }} URLs
                            </span>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-600">URL</p>
                                <a href="{{ $sitemapStats['agencies_sitemap']['url'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium break-all">
                                    {{ $sitemapStats['agencies_sitemap']['url'] }}
                                </a>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Agencies</p>
                                <p class="text-sm font-medium">{{ number_format($sitemapStats['agencies_sitemap']['count']) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Last Updated</p>
                                <p class="text-sm font-medium">
                                    {{ $sitemapStats['agencies_sitemap']['last_updated'] ? $sitemapStats['agencies_sitemap']['last_updated']->diffForHumans() : 'Never' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Locations Sitemap -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Locations Sitemap</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                {{ number_format($sitemapStats['locations_sitemap']['count']) }} URLs
                            </span>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-600">URL</p>
                                <a href="{{ $sitemapStats['locations_sitemap']['url'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium break-all">
                                    {{ $sitemapStats['locations_sitemap']['url'] }}
                                </a>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Locations</p>
                                <p class="text-sm font-medium">{{ number_format($sitemapStats['locations_sitemap']['count']) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Last Updated</p>
                                <p class="text-sm font-medium">
                                    {{ $sitemapStats['locations_sitemap']['last_updated'] ? $sitemapStats['locations_sitemap']['last_updated']->diffForHumans() : 'Never' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Images Sitemap -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Images Sitemap</h3>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                                {{ number_format($sitemapStats['images_sitemap']['count']) }} Images
                            </span>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-600">URL</p>
                                <a href="{{ $sitemapStats['images_sitemap']['url'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm font-medium break-all">
                                    {{ $sitemapStats['images_sitemap']['url'] }}
                                </a>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Images</p>
                                <p class="text-sm font-medium">{{ number_format($sitemapStats['images_sitemap']['count']) }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Last Updated</p>
                                <p class="text-sm font-medium">
                                    {{ $sitemapStats['images_sitemap']['last_updated'] ? $sitemapStats['images_sitemap']['last_updated']->diffForHumans() : 'Never' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sitemap Submission Status -->
            <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Search Engine Submission Status</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.042-3.441.219-.937 1.404-5.965 1.404-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738a.36.36 0 01.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.889-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.357-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                                </svg>
                            </div>
                            <h4 class="font-medium text-gray-900">Google</h4>
                            <p class="text-sm text-green-600 mt-1">Submitted</p>
                            <p class="text-xs text-gray-500 mt-1">Last: 2 days ago</p>
                        </div>
                        
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M13.632 13.635h2.37V24H13.632zm-2.37 0V24H8.892V13.635zM24 7.804V24h-2.37V7.804zm-21.63 0V24H0V7.804zM7.804 0v24H5.434V0zm8.392 0v24h-2.37V0z"/>
                                </svg>
                            </div>
                            <h4 class="font-medium text-gray-900">Bing</h4>
                            <p class="text-sm text-yellow-600 mt-1">Pending</p>
                            <p class="text-xs text-gray-500 mt-1">Submit manually</p>
                        </div>
                        
                        <div class="text-center p-4 border border-gray-200 rounded-lg">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.238 15.348c.085-.297.154-.543.154-.889 0-.333-.069-.585-.154-.889l-.949.889.949.889zm4.262-4.348c0 6.627-5.373 12-12 12s-12-5.373-12-12 5.373-12 12-12 12 5.373 12 12zm-5.5 0c0-.394-.106-.806-.287-1.114-.181-.308-.438-.554-.725-.725-.287-.171-.699-.287-1.114-.287-.394 0-.806.106-1.114.287-.308.181-.554.438-.725.725-.171.287-.287.699-.287 1.114 0 .394.106.806.287 1.114.181.308.438.554.725.725.287.171.699.287 1.114.287.394 0 .806-.106 1.114-.287.308-.181.554-.438.725-.725.171-.287.287-.699.287-1.114z"/>
                                </svg>
                            </div>
                            <h4 class="font-medium text-gray-900">Yandex</h4>
                            <p class="text-sm text-gray-600 mt-1">Not Submitted</p>
                            <p class="text-xs text-gray-500 mt-1">Optional</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sitemap Guidelines -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Sitemap Guidelines</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Best Practices</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Sitemaps are automatically updated when content changes</li>
                            <li>• Maximum 50,000 URLs per sitemap file</li>
                            <li>• Images are included with proper captions and titles</li>
                            <li>• Priority and change frequency are optimized</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-blue-800 mb-2">Submission URLs</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Google: <a href="https://search.google.com/search-console" target="_blank" class="underline">Search Console</a></li>
                            <li>• Bing: <a href="https://www.bing.com/webmasters" target="_blank" class="underline">Webmaster Tools</a></li>
                            <li>• Yandex: <a href="https://webmaster.yandex.com" target="_blank" class="underline">Webmaster</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
