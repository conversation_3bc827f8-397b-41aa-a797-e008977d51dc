<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('SEO Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.seo.sitemaps') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Sitemaps
                </a>
                <a href="{{ route('admin.seo.analytics') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Analytics
                </a>
                <a href="{{ route('admin.seo.performance') }}" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    Performance
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- SEO Overview Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Pages -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Pages</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($seoOverview['total_pages']) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Indexed Pages -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Indexed Pages</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $seoOverview['indexed_pages'] }}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Average Position -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Avg. Position</p>
                                <p class="text-2xl font-semibold text-gray-900">#{{ $seoOverview['avg_position'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Organic Traffic -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Monthly Organic Traffic</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($seoOverview['organic_traffic']) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Keyword Rankings -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Keyword Rankings</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600">{{ $seoOverview['keyword_rankings']['top_3'] }}</p>
                                <p class="text-sm text-gray-600">Top 3 Rankings</p>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <p class="text-2xl font-bold text-blue-600">{{ $seoOverview['keyword_rankings']['top_10'] }}</p>
                                <p class="text-sm text-gray-600">Top 10 Rankings</p>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                <p class="text-2xl font-bold text-yellow-600">{{ $seoOverview['keyword_rankings']['top_50'] }}</p>
                                <p class="text-sm text-gray-600">Top 50 Rankings</p>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <p class="text-2xl font-bold text-gray-600">{{ $seoOverview['keyword_rankings']['total_tracked'] }}</p>
                                <p class="text-sm text-gray-600">Total Tracked</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="{{ route('admin.seo.keywords') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View All Keywords →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Lighthouse Score</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $performanceMetrics['lighthouse_score'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ $performanceMetrics['lighthouse_score'] }}</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Page Load Time</span>
                                <span class="text-sm font-medium {{ $performanceMetrics['page_load_time'] < 3 ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $performanceMetrics['page_load_time'] }}s
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Images Optimized</span>
                                <span class="text-sm font-medium">{{ $performanceMetrics['images_optimized'] }}%</span>
                            </div>
                            <div class="grid grid-cols-2 gap-2 mt-4">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-{{ $performanceMetrics['mobile_friendly'] ? 'green' : 'red' }}-500 rounded-full mr-2"></span>
                                    <span class="text-xs text-gray-600">Mobile Friendly</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-{{ $performanceMetrics['ssl_enabled'] ? 'green' : 'red' }}-500 rounded-full mr-2"></span>
                                    <span class="text-xs text-gray-600">SSL Enabled</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Analysis -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Content Analysis</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Pages with Meta Titles</span>
                                <span class="text-sm font-medium text-green-600">{{ number_format($contentAnalysis['pages_with_meta_titles']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Pages with Meta Descriptions</span>
                                <span class="text-sm font-medium text-green-600">{{ number_format($contentAnalysis['pages_with_meta_descriptions']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Pages with H1 Tags</span>
                                <span class="text-sm font-medium text-green-600">{{ number_format($contentAnalysis['pages_with_h1_tags']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Images with Alt Text</span>
                                <span class="text-sm font-medium text-green-600">{{ number_format($contentAnalysis['images_with_alt_text']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Internal Links</span>
                                <span class="text-sm font-medium">{{ number_format($contentAnalysis['internal_links']) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Duplicate Content Issues</span>
                                <span class="text-sm font-medium {{ $contentAnalysis['duplicate_content'] == 0 ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $contentAnalysis['duplicate_content'] }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical SEO Status -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Technical SEO Status</h3>
                        <div class="grid grid-cols-2 gap-3">
                            @foreach($technicalSeo as $item => $status)
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-{{ $status ? 'green' : 'red' }}-500 rounded-full mr-2"></span>
                                    <span class="text-xs text-gray-600">{{ ucwords(str_replace('_', ' ', $item)) }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent SEO Activities</h3>
                    <div class="space-y-3">
                        @foreach($recentActivities as $activity)
                            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                                <span class="text-sm text-gray-700">{{ $activity['action'] }}</span>
                                <span class="text-xs text-gray-500">{{ $activity['date']->diffForHumans() }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <form action="{{ route('admin.seo.regenerate-sitemaps') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Regenerate Sitemaps
                            </button>
                        </form>
                        <form action="{{ route('admin.seo.clear-caches') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                Clear Performance Caches
                            </button>
                        </form>
                        <a href="{{ route('sitemap.index') }}" target="_blank" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center inline-block">
                            View Main Sitemap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
