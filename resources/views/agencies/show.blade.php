<x-home-layout>
    <!-- Agency Header Section -->
    <div>
        <!-- Hero Banner -->
        <div class="bg-gradient-to-r from-indigo-800 via-purple-700 to-pink-700 h-80 md:h-96 relative overflow-hidden">
            <!-- Pattern overlay -->
            <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

            <!-- Dot Pattern Overlay -->
            <div class="absolute inset-0 bg-pattern opacity-30" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC42Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+');"></div>

            <!-- Dark overlay gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/20"></div>

            <!-- Agency name overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-6 md:p-8 text-white z-10 pb-16 md:pb-20">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
                        <div>
                            <h1 class="text-3xl md:text-5xl font-bold text-white drop-shadow-lg tracking-tight">{{ $agency->name }}</h1>
                        </div>
                        <div class="flex items-center gap-3">
                            @if($agency->is_verified)
                                <span class="inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800 shadow-md border border-green-200">
                                    <svg class="mr-1.5 h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-semibold">Verified</span>
                                </span>
                            @endif


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agency Info Section -->
    <div class="bg-gray-50 py-10 relative">
        <!-- Subtle pattern background -->
        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%239C92AC" fill-opacity="0.4" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="1"%2F%3E%3Ccircle cx="13" cy="13" r="1"%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="bg-white rounded-lg shadow-md -mt-24 mb-10 overflow-hidden border border-gray-100 relative z-20">
                <!-- Agency Info Card -->
                <div class="p-6 md:p-8">
                    <div class="flex flex-col md:flex-row items-center md:items-start gap-6 md:gap-10">
                        <!-- Agency Logo -->
                        <div class="w-36 h-36 md:w-48 md:h-48 bg-white rounded-lg shadow-md overflow-hidden border-2 border-white ring-1 ring-gray-100 transform transition-all duration-300 hover:scale-105 hover:shadow-lg">
                            @if($agency->logo_path)
                                <img
                                    src="{{ asset('storage/' . $agency->logo_path) }}"
                                    alt="{{ $agency->name }}"
                                    class="w-full h-full object-contain p-3"
                                    onerror="this.onerror=null; this.src='{{ asset('images/default-agency-logo.png') }}'"
                                >
                            @else
                                <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
                                    <svg class="w-20 h-20 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Agency Info -->
                        <div class="text-center md:text-left flex-1 mt-2">
                            <!-- Contact Info Cards -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-gray-700 text-sm">
                                @if($agency->address)
                                    <div class="flex items-center bg-gray-50 px-5 py-3.5 rounded-lg border border-gray-100 shadow-sm hover:shadow transition-all duration-300 hover:border-pink-200 hover:-translate-y-0.5 h-full">
                                        <div class="bg-gradient-to-br from-pink-100 to-pink-200 p-2.5 rounded-full mr-3.5 flex-shrink-0">
                                            <svg class="w-5 h-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                        <span class="font-medium text-gray-800 truncate">{{ $agency->address }}</span>
                                    </div>
                                @endif

                                @if($agency->phone)
                                    <div class="flex items-center bg-gray-50 px-5 py-3.5 rounded-lg border border-gray-100 shadow-sm hover:shadow transition-all duration-300 hover:border-pink-200 hover:-translate-y-0.5 h-full">
                                        <div class="bg-gradient-to-br from-indigo-100 to-indigo-200 p-2.5 rounded-full mr-3.5 flex-shrink-0">
                                            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                        </div>
                                        <span class="font-medium text-gray-800 truncate">{{ $agency->phone }}</span>
                                    </div>
                                @endif

                                @if($agency->email)
                                    <div class="flex items-center bg-gray-50 px-5 py-3.5 rounded-lg border border-gray-100 shadow-sm hover:shadow transition-all duration-300 hover:border-pink-200 hover:-translate-y-0.5 h-full">
                                        <div class="bg-gradient-to-br from-purple-100 to-purple-200 p-2.5 rounded-full mr-3.5 flex-shrink-0">
                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <span class="font-medium text-gray-800 truncate">{{ $agency->email }}</span>
                                    </div>
                                @endif

                                @if($agency->website)
                                    <div class="flex items-center bg-gray-50 px-5 py-3.5 rounded-lg border border-gray-100 shadow-sm hover:shadow transition-all duration-300 hover:border-pink-200 hover:-translate-y-0.5 h-full">
                                        <div class="bg-gradient-to-br from-blue-100 to-blue-200 p-2.5 rounded-full mr-3.5 flex-shrink-0">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                            </svg>
                                        </div>
                                        <a href="{{ $agency->website }}" target="_blank" class="font-medium text-gray-800 hover:text-pink-600 transition-colors duration-300 truncate">{{ $agency->website }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="p-6 md:p-8 bg-gray-50 border-t border-gray-200">
                    <div class="flex flex-wrap justify-center md:justify-start gap-5">
                        @if($agency->phone)
                            <a href="tel:{{ $agency->phone }}" class="group bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-medium py-4 px-8 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:shadow-pink-200/50">
                                <div class="bg-white/20 p-2 rounded-full mr-3.5 group-hover:bg-white/30 transition-all duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <span class="tracking-wide">Call Now</span>
                            </a>
                        @endif

                        <a href="#escorts" class="group bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white font-medium py-4 px-8 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:shadow-indigo-200/50">
                            <div class="bg-white/20 p-2 rounded-full mr-3.5 group-hover:bg-white/30 transition-all duration-300">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                </svg>
                            </div>
                            <span class="tracking-wide">View {{ $totalEscorts }} Escorts</span>
                        </a>

                        @if($agency->website)
                            <a href="{{ $agency->website }}" target="_blank" class="group bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium py-4 px-8 rounded-lg flex items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:shadow-purple-200/50">
                                <div class="bg-white/20 p-2 rounded-full mr-3.5 group-hover:bg-white/30 transition-all duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </div>
                                <span class="tracking-wide">Visit Website</span>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agency Content Section -->
    <div class="py-12 relative">
        <!-- Subtle pattern background -->
        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M0 0h20v20H0V0zm10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14z" fill="%239C92AC" fill-opacity="0.4" fill-rule="evenodd"%2F%3E%3C%2Fsvg%3E');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: About Agency -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8 border border-gray-100">
                        <div class="border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white py-4 px-6">
                            <h2 class="text-2xl font-bold text-gray-900">About {{ $agency->name }}</h2>
                        </div>
                        <div class="p-6">
                            @if($agency->description)
                                <div class="prose max-w-none mb-6">
                                    <p class="text-gray-700 leading-relaxed">{{ $agency->description }}</p>
                                </div>
                            @else
                                <div class="bg-gray-50 rounded-lg p-6 text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <p class="text-gray-500">No description available for this agency.</p>
                                </div>
                            @endif

                            <!-- Agency Stats Summary -->
                            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-pink-50 rounded-lg p-4 text-center">
                                    <div class="text-3xl font-bold text-pink-600 mb-1">{{ $totalEscorts }}</div>
                                    <div class="text-sm text-gray-600">Escorts</div>
                                </div>

                                <div class="bg-purple-50 rounded-lg p-4 text-center">
                                    <div class="text-3xl font-bold text-purple-600 mb-1">{{ $agency->profile_views }}</div>
                                    <div class="text-sm text-gray-600">Profile Views</div>
                                </div>

                                <div class="bg-green-50 rounded-lg p-4 text-center">
                                    <div class="text-3xl font-bold text-green-600 mb-1">{{ $agency->is_verified ? 'Yes' : 'No' }}</div>
                                    <div class="text-sm text-gray-600">Verified</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agency Escorts Section -->
                    <div id="escorts" class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
                        <div class="border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white py-4 px-6 flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-900">Our Escorts</h2>
                            <span class="text-sm text-gray-500">{{ $totalEscorts }} {{ Str::plural('Escort', $totalEscorts) }}</span>
                        </div>

                        <div class="p-6">
                            <!-- Featured Escorts Section -->
                            @if($featuredEscorts->count() > 0)
                                <div class="mb-8">
                                    <h3 class="text-lg font-semibold text-pink-600 mb-4 flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        Featured Escorts
                                    </h3>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
                                        @foreach($featuredEscorts as $escort)
                                            <div class="bg-white border border-pink-100 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                                                <a href="{{ route('escorts.show', $escort->slug) }}" class="block">
                                                    <div class="relative">
                                                        @if($escort->images->count() > 0)
                                                            <img
                                                                src="{{ asset('storage/' . $escort->images->first()->image_path) }}"
                                                                alt="{{ $escort->name }}"
                                                                class="w-full h-56 object-cover"
                                                            >
                                                            <!-- Watermark -->
                                                            <div class="absolute inset-0 flex items-center justify-center">
                                                                <span class="text-white text-opacity-30 font-bold text-2xl transform -rotate-12">GHB</span>
                                                            </div>
                                                        @else
                                                            <div class="w-full h-56 bg-gray-200 flex items-center justify-center">
                                                                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </div>
                                                        @endif

                                                        <!-- Status Badges -->
                                                        <div class="absolute top-2 right-2 flex flex-col space-y-1">
                                                            @if($escort->is_verified)
                                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-md shadow-sm">Verified</span>
                                                            @endif

                                                            <span class="bg-gradient-to-r from-pink-500 to-pink-600 text-white text-xs px-2 py-1 rounded-md shadow-sm">Featured</span>
                                                        </div>

                                                        <!-- Escort Info Overlay -->
                                                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                                                            <h3 class="text-white font-semibold text-lg">{{ $escort->name }}</h3>
                                                            <div class="flex items-center text-gray-200 text-sm">
                                                                <span>{{ $escort->age }} yrs</span>
                                                                @if($escort->locations->count() > 0)
                                                                    <span class="mx-2">•</span>
                                                                    <span>{{ $escort->locations->first()->name }}</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Regular Escorts Section -->
                            @if($regularEscorts->count() > 0)
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-700 mb-4">All Escorts</h3>

                                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($regularEscorts as $escort)
                                            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300">
                                                <a href="{{ route('escorts.show', $escort->slug) }}" class="block">
                                                    <div class="relative">
                                                        @if($escort->images->count() > 0)
                                                            <img
                                                                src="{{ asset('storage/' . $escort->images->first()->image_path) }}"
                                                                alt="{{ $escort->name }}"
                                                                class="w-full h-48 object-cover"
                                                            >
                                                            <!-- Watermark -->
                                                            <div class="absolute inset-0 flex items-center justify-center">
                                                                <span class="text-white text-opacity-30 font-bold text-2xl transform -rotate-12">GHB</span>
                                                            </div>
                                                        @else
                                                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                                                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </div>
                                                        @endif

                                                        <!-- Status Badges -->
                                                        <div class="absolute top-2 right-2 flex flex-col space-y-1">
                                                            @if($escort->is_verified)
                                                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-md">Verified</span>
                                                            @endif
                                                        </div>

                                                        <!-- Escort Info Overlay -->
                                                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
                                                            <h3 class="text-white font-semibold">{{ $escort->name }}</h3>
                                                            <div class="flex items-center text-gray-200 text-sm">
                                                                <span>{{ $escort->age }} yrs</span>
                                                                @if($escort->locations->count() > 0)
                                                                    <span class="mx-2">•</span>
                                                                    <span>{{ $escort->locations->first()->name }}</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>

                                    @if($totalEscorts > count($regularEscorts) + count($featuredEscorts))
                                        <div class="mt-6 text-center">
                                            <a href="#" class="inline-flex items-center px-4 py-2 border border-pink-300 text-sm font-medium rounded-md text-pink-700 bg-white hover:bg-pink-50 transition-colors duration-300">
                                                View All {{ $totalEscorts }} Escorts
                                                <svg class="ml-2 -mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            @endif

                            @if($featuredEscorts->count() == 0 && $regularEscorts->count() == 0)
                                <div class="bg-gray-50 rounded-lg p-8 text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No escorts available</h3>
                                    <p class="text-gray-600">This agency doesn't have any escorts listed at the moment.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Right Column: Contact and Info -->
                <div>
                    <!-- Quick Contact Card -->
                    <div class="bg-white rounded-lg shadow-md mb-8 border border-gray-100">
                        <div class="border-b border-gray-100 bg-gray-50 py-4 px-6">
                            <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                Contact {{ $agency->name }}
                            </h2>
                        </div>
                        <div class="p-6">
                            @if($agency->phone)
                                <a href="tel:{{ $agency->phone }}" class="block w-full bg-pink-500 hover:bg-pink-600 text-white font-medium py-3 px-4 rounded-lg text-center shadow mb-4">
                                    <div class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                        <span>{{ $agency->phone }}</span>
                                    </div>
                                </a>
                            @endif

                            @if($agency->email)
                                <a href="mailto:{{ $agency->email }}" class="block w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-3 px-4 rounded-lg text-center shadow mb-4">
                                    <div class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>Email Us</span>
                                    </div>
                                </a>
                            @endif

                            @if($agency->website)
                                <a href="{{ $agency->website }}" target="_blank" class="block w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-lg text-center shadow">
                                    <div class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                        </svg>
                                        <span>Visit Website</span>
                                    </div>
                                </a>
                            @endif
                        </div>
                    </div>

                    <!-- Agency Information Card -->
                    <div class="bg-white rounded-lg shadow-md mb-8 border border-gray-100">
                        <div class="border-b border-gray-100 bg-gray-50 py-4 px-6">
                            <h2 class="text-xl font-bold text-gray-900">Agency Information</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100">
                                    <div class="bg-pink-100 rounded-full p-2 mr-4">
                                        <svg class="w-6 h-6 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Escorts</div>
                                        <div class="font-semibold text-gray-900">{{ $totalEscorts }}</div>
                                    </div>
                                </div>

                                <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100">
                                    <div class="bg-blue-100 rounded-full p-2 mr-4">
                                        <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Member Since</div>
                                        <div class="font-semibold text-gray-900">{{ $agency->created_at->format('M Y') }}</div>
                                    </div>
                                </div>

                                <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100">
                                    <div class="bg-purple-100 rounded-full p-2 mr-4">
                                        <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-500">Profile Views</div>
                                        <div class="font-semibold text-gray-900">{{ $agency->profile_views }}</div>
                                    </div>
                                </div>

                                @if($agency->address)
                                    <div class="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100">
                                        <div class="bg-green-100 rounded-full p-2 mr-4">
                                            <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm text-gray-500">Location</div>
                                            <div class="font-semibold text-gray-900">{{ $agency->address }}</div>
                                        </div>
                                    </div>
                                @endif

                                <div class="flex flex-wrap gap-2 mt-4">
                                    @if($agency->is_verified)
                                        <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Verified Agency
                                        </div>
                                    @endif


                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Safety Tips -->
                    <div class="bg-white rounded-lg shadow-md mb-8 border border-gray-100">
                        <div class="border-b border-gray-100 bg-gray-50 py-4 px-6">
                            <h2 class="text-xl font-bold text-gray-900">Safety Tips</h2>
                        </div>
                        <div class="p-6">
                            <ul class="space-y-3 text-gray-600">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Always verify the agency's credentials before booking</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Meet in public places for initial meetings</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Trust your instincts and prioritize your safety</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Inform someone you trust about your plans</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-home-layout>