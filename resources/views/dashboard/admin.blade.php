<x-app-layout>
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <!-- Admin Dashboard Header -->
        <div class="bg-gradient-to-r from-purple-800 via-indigo-900 to-blue-900 shadow-2xl">
            <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-4">
                            <div class="bg-white bg-opacity-20 rounded-xl p-3">
                                <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-white sm:text-4xl">
                                    Admin Control Center
                                </h1>
                                <p class="mt-2 text-lg text-indigo-100">
                                    Comprehensive platform management and analytics
                                </p>
                            </div>
                        </div>
                        <div class="mt-4 flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-4 sm:gap-6">
                            <div class="flex items-center text-sm text-indigo-100">
                                <svg class="flex-shrink-0 mr-2 h-5 w-5 text-indigo-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                </svg>
                                <span class="hidden sm:inline">{{ now()->format('l, F j, Y \a\t g:i A') }}</span>
                                <span class="sm:hidden">{{ now()->format('M j, Y g:i A') }}</span>
                            </div>
                            <div class="flex items-center text-sm text-indigo-100">
                                <svg class="flex-shrink-0 mr-2 h-5 w-5 text-indigo-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                                </svg>
                                System Status: <span class="text-green-300 font-medium">Online</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3">
                        <a href="{{ route('admin.users.create') }}" class="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                            </svg>
                            <span class="hidden sm:inline">Add User</span>
                            <span class="sm:hidden">User</span>
                        </a>
                        <a href="{{ route('admin.status-pricing.index') }}" class="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd" />
                            </svg>
                            Pricing
                        </a>
                        <a href="{{ route('admin.announcements.create') }}" class="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z" clip-rule="evenodd" />
                            </svg>
                            <span class="hidden sm:inline">Announce</span>
                            <span class="sm:hidden">News</span>
                        </a>
                        <a href="{{ route('admin.settings') }}" class="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-white border-opacity-30 rounded-lg shadow-sm text-sm font-medium text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                            </svg>
                            Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 space-y-8">
            <!-- Enhanced Statistics Overview -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-4 sm:gap-6">
                <!-- Revenue Card -->
                <div class="xl:col-span-1">
                    <div class="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl shadow-xl overflow-hidden">
                        <div class="p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div class="min-w-0 flex-1">
                                    <p class="text-emerald-100 text-sm font-medium">Total Revenue</p>
                                    <p class="text-2xl sm:text-3xl font-bold text-white truncate">
                                        UGX {{ number_format($metrics['revenue']['total'], 0) }}
                                    </p>
                                    <p class="text-emerald-100 text-xs mt-1">All time earnings</p>
                                </div>
                                <div class="bg-white bg-opacity-20 rounded-xl p-2 sm:p-3 flex-shrink-0">
                                    <svg class="h-6 w-6 sm:h-8 sm:w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-emerald-100 text-sm">
                                    <svg class="h-4 w-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="truncate">This month: UGX {{ number_format($metrics['revenue']['monthly'], 0) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Stats Grid -->
                <div class="xl:col-span-3">
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
                        <!-- Users Stat -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="p-4 sm:p-5">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-gray-500 text-sm font-medium">Total Users</p>
                                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $metrics['users']['total'] }}</p>
                                        <p class="text-xs text-gray-400 mt-1">{{ $metrics['users']['admins'] }} Admins</p>
                                    </div>
                                    <div class="bg-indigo-100 rounded-lg p-2 sm:p-3 flex-shrink-0">
                                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.users') }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors duration-200">Manage →</a>
                                </div>
                            </div>
                        </div>

                        <!-- Escorts Stat -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="p-4 sm:p-5">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-gray-500 text-sm font-medium">Total Escorts</p>
                                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $metrics['escorts']['total'] }}</p>
                                        <p class="text-xs text-gray-400 mt-1">{{ $metrics['escorts']['verified'] }} Verified</p>
                                    </div>
                                    <div class="bg-pink-100 rounded-lg p-2 sm:p-3 flex-shrink-0">
                                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-pink-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.escorts') }}" class="text-pink-600 hover:text-pink-800 text-sm font-medium transition-colors duration-200">Manage →</a>
                                </div>
                            </div>
                        </div>

                        <!-- Agencies Stat -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="p-4 sm:p-5">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-gray-500 text-sm font-medium">Total Agencies</p>
                                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $metrics['agencies']['total'] }}</p>
                                        <p class="text-xs text-gray-400 mt-1">{{ $metrics['agencies']['verified'] }} Verified</p>
                                    </div>
                                    <div class="bg-blue-100 rounded-lg p-2 sm:p-3 flex-shrink-0">
                                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.agencies') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">Manage →</a>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Requests Stat -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300 sm:col-span-2 xl:col-span-1">
                            <div class="p-4 sm:p-5">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-gray-500 text-sm font-medium">Pending Requests</p>
                                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $metrics['requests']['escort_requests']['pending'] + $metrics['requests']['agency_requests']['pending'] }}</p>
                                        <p class="text-xs text-gray-400 mt-1">Awaiting review</p>
                                    </div>
                                    <div class="bg-amber-100 rounded-lg p-2 sm:p-3 flex-shrink-0">
                                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('admin.all-requests.index') }}?status=pending" class="text-amber-600 hover:text-amber-800 text-sm font-medium transition-colors duration-200">Review →</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Management Hub -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                <!-- Request Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-4 sm:px-6 py-4">
                        <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Request Management
                        </h3>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">
                        <div class="flex items-center justify-between p-3 bg-amber-50 rounded-lg border border-amber-200">
                            <div class="min-w-0 flex-1">
                                <p class="text-sm font-medium text-amber-800">Pending Escort Requests</p>
                                <p class="text-xl sm:text-2xl font-bold text-amber-900">{{ $metrics['requests']['escort_requests']['pending'] }}</p>
                            </div>
                            <a href="{{ route('admin.status-requests.index') }}?status=pending" class="text-amber-600 hover:text-amber-800 transition-colors duration-200 flex-shrink-0">
                                <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div class="min-w-0 flex-1">
                                <p class="text-sm font-medium text-blue-800">Pending Agency Requests</p>
                                <p class="text-xl sm:text-2xl font-bold text-blue-900">{{ $metrics['requests']['agency_requests']['pending'] }}</p>
                            </div>
                            <a href="{{ route('admin.agency-status-requests.index') }}?status=pending" class="text-blue-600 hover:text-blue-800 transition-colors duration-200 flex-shrink-0">
                                <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <div class="pt-2">
                            <a href="{{ route('admin.status-requests.index') }}" class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 transition-colors duration-200">
                                View All Requests
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Pricing Management -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-emerald-600 to-teal-600 px-4 sm:px-6 py-4">
                        <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Pricing Control
                        </h3>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">

                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center p-3 bg-purple-50 rounded-lg">
                                <p class="text-xs text-purple-600 font-medium">Verification</p>
                                <p class="text-base sm:text-lg font-bold text-purple-900">{{ $metrics['pricing']['verification'] }}</p>
                                <p class="text-xs text-purple-500">Active Plans</p>
                            </div>
                            <div class="text-center p-3 bg-amber-50 rounded-lg">
                                <p class="text-xs text-amber-600 font-medium">Featured</p>
                                <p class="text-base sm:text-lg font-bold text-amber-900">{{ $metrics['pricing']['featured'] }}</p>
                                <p class="text-xs text-amber-500">Active Plans</p>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <p class="text-xs text-blue-600 font-medium">Agency Approval</p>
                                <p class="text-base sm:text-lg font-bold text-blue-900">{{ $metrics['pricing']['agency_approval'] }}</p>
                                <p class="text-xs text-blue-500">Active Plans</p>
                            </div>
                            <div class="text-center p-3 bg-indigo-50 rounded-lg">
                                <p class="text-xs text-indigo-600 font-medium">Agency Featured</p>
                                <p class="text-base sm:text-lg font-bold text-indigo-900">{{ $metrics['pricing']['agency_featured'] }}</p>
                                <p class="text-xs text-indigo-500">Active Plans</p>
                            </div>
                        </div>
                        <div class="pt-2 space-y-2">
                            <a href="{{ route('admin.status-pricing.index') }}" class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 transition-colors duration-200">
                                Manage Pricing
                            </a>
                            <a href="{{ route('admin.status-pricing.create') }}" class="w-full flex items-center justify-center px-4 py-2 border border-emerald-300 rounded-lg shadow-sm text-sm font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 transition-colors duration-200">
                                Add New Plan
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Analytics -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-indigo-600 to-blue-600 px-4 sm:px-6 py-4">
                        <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            System Analytics
                        </h3>
                    </div>
                    <div class="p-4 sm:p-6 space-y-4">

                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span class="text-sm text-gray-600">New Users Today</span>
                                <span class="font-semibold text-gray-900">{{ $metrics['users']['today'] }}</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span class="text-sm text-gray-600">This Week</span>
                                <span class="font-semibold text-gray-900">{{ $metrics['users']['week'] }}</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span class="text-sm text-gray-600">This Month</span>
                                <span class="font-semibold text-gray-900">{{ $metrics['users']['month'] }}</span>
                            </div>
                        </div>
                        <div class="pt-2 space-y-2">
                            <a href="{{ route('admin.users') }}" class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                                User Management
                            </a>
                            <a href="{{ route('admin.settings') }}" class="w-full flex items-center justify-center px-4 py-2 border border-indigo-300 rounded-lg shadow-sm text-sm font-medium text-indigo-700 bg-indigo-50 hover:bg-indigo-100 transition-colors duration-200">
                                System Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity and Financial Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <!-- Recent Activity -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-4 sm:px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                                <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                <span class="hidden sm:inline">Recent Activity</span>
                                <span class="sm:hidden">Activity</span>
                            </h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></span>
                                Live
                            </span>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6">

                        <div class="flow-root">
                            <ul role="list" class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-pink-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <a href="#" class="font-medium text-gray-900">New escort registered</a>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">10 minutes ago</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>A new escort profile was created in the system.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <a href="#" class="font-medium text-gray-900">Verification request approved</a>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">1 hour ago</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>An escort verification request was approved.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li>
                                    <div class="relative pb-8">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h6v4H7V5zm8 8v2h1v1H4v-1h1v-2H4v-1h16v1h-1zm-2 0H7v2h6v-2z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <a href="#" class="font-medium text-gray-900">New agency created</a>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">3 hours ago</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>A new agency account was registered.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li>
                                    <div class="relative pb-8">
                                        <div class="relative flex items-start space-x-3">
                                            <div class="relative">
                                                <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div>
                                                    <div class="text-sm">
                                                        <a href="#" class="font-medium text-gray-900">New location added</a>
                                                    </div>
                                                    <p class="mt-0.5 text-sm text-gray-500">5 hours ago</p>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-700">
                                                    <p>A new location was added to the system.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="mt-6">
                            <a href="#" class="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                View all activity
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Financial Overview -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="bg-gradient-to-r from-emerald-600 to-green-600 px-4 sm:px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                                <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <span class="hidden sm:inline">Financial Overview</span>
                                <span class="sm:hidden">Finance</span>
                            </h3>
                            <a href="{{ route('admin.all-requests.index') }}" class="text-emerald-100 hover:text-white text-sm font-medium transition-colors duration-200">
                                <span class="hidden sm:inline">View Details →</span>
                                <span class="sm:hidden">Details →</span>
                            </a>
                        </div>
                    </div>
                    <div class="p-4 sm:p-6">



                        <!-- Revenue Summary -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-medium text-blue-600">Today's Revenue</p>
                                        <p class="text-xl sm:text-2xl font-bold text-blue-900 truncate">UGX {{ number_format($metrics['revenue']['daily'], 0) }}</p>
                                    </div>
                                    <div class="bg-blue-100 rounded-lg p-2 flex-shrink-0">
                                        <svg class="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl p-4 border border-emerald-100">
                                <div class="flex items-center justify-between">
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-medium text-emerald-600">This Week</p>
                                        <p class="text-xl sm:text-2xl font-bold text-emerald-900 truncate">UGX {{ number_format($metrics['revenue']['weekly'], 0) }}</p>
                                    </div>
                                    <div class="bg-emerald-100 rounded-lg p-2 flex-shrink-0">
                                        <svg class="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Revenue Breakdown -->
                        <div class="space-y-4">
                            <h4 class="text-sm font-semibold text-gray-900">Revenue Breakdown</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 rounded-lg p-2 mr-3">
                                            <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-purple-900">Escort Services</p>
                                            <p class="text-xs text-purple-600">{{ $metrics['requests']['escort_requests']['approved'] }} transactions</p>
                                        </div>
                                    </div>
                                    <p class="text-sm font-bold text-purple-900">UGX {{ number_format($metrics['revenue']['escort_revenue'], 0) }}</p>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 rounded-lg p-2 mr-3">
                                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-blue-900">Agency Services</p>
                                            <p class="text-xs text-blue-600">{{ $metrics['requests']['agency_requests']['approved'] }} transactions</p>
                                        </div>
                                    </div>
                                    <p class="text-sm font-bold text-blue-900">UGX {{ number_format($metrics['revenue']['agency_revenue'], 0) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Quick Actions Section -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div class="bg-gradient-to-r from-slate-800 to-slate-900 px-4 sm:px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-base sm:text-lg font-semibold text-white flex items-center">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span class="hidden sm:inline">Administrative Actions</span>
                            <span class="sm:hidden">Actions</span>
                        </h3>
                        <span class="text-slate-300 text-sm hidden sm:inline">Quick Access</span>
                    </div>
                </div>
                <div class="p-4 sm:p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Add Escort -->
                        <a href="{{ route('admin.escorts.create') }}" class="group block p-4 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-white font-semibold text-sm mb-1">Add Escort</h4>
                            <p class="text-pink-100 text-xs">Create new profile</p>
                        </a>

                        <!-- Add Agency -->
                        <a href="{{ route('admin.agencies.create') }}" class="group block p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-white font-semibold text-sm mb-1">Add Agency</h4>
                            <p class="text-blue-100 text-xs">Create new agency</p>
                        </a>

                        <!-- Manage Pricing -->
                        <a href="{{ route('admin.status-pricing.index') }}" class="group block p-4 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-white font-semibold text-sm mb-1">Pricing</h4>
                            <p class="text-emerald-100 text-xs">Manage rates</p>
                        </a>

                        <!-- Review Requests -->
                        <a href="{{ route('admin.all-requests.index') }}" class="group block p-4 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                    {{ $metrics['requests']['escort_requests']['pending'] + $metrics['requests']['agency_requests']['pending'] }}
                                </div>
                            </div>
                            <h4 class="text-white font-semibold text-sm mb-1">Requests</h4>
                            <p class="text-amber-100 text-xs">Review pending</p>
                        </a>
                    </div>

                    <!-- Additional Actions Row -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mt-4">
                        <!-- User Management -->
                        <a href="{{ route('admin.users') }}" class="group block p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-indigo-100 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-gray-900 font-semibold text-sm mb-1">Users</h4>
                            <p class="text-gray-500 text-xs">Manage accounts</p>
                        </a>

                        <!-- Announcements -->
                        <a href="{{ route('admin.announcements.create') }}" class="group block p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-purple-100 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-purple-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-gray-900 font-semibold text-sm mb-1">Announce</h4>
                            <p class="text-gray-500 text-xs">Send notifications</p>
                        </a>

                        <!-- Settings -->
                        <a href="{{ route('admin.settings') }}" class="group block p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-gray-400 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-gray-100 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-gray-900 font-semibold text-sm mb-1">Settings</h4>
                            <p class="text-gray-500 text-xs">System config</p>
                        </a>

                        <!-- Locations -->
                        <a href="{{ route('admin.locations') }}" class="group block p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-emerald-300 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-emerald-100 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-emerald-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-emerald-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-gray-900 font-semibold text-sm mb-1">Locations</h4>
                            <p class="text-gray-500 text-xs">Manage areas</p>
                        </a>

                        <!-- All Requests -->
                        <a href="{{ route('admin.all-requests.index') }}" class="group block p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-green-300 hover:shadow-md transition-all duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <div class="bg-green-100 rounded-lg p-2">
                                    <svg class="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-gray-900 font-semibold text-sm mb-1">All Requests</h4>
                            <p class="text-gray-500 text-xs">Manage requests</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
