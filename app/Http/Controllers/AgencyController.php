<?php

namespace App\Http\Controllers;

use App\Models\Agency;
use App\Services\SeoService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class AgencyController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get featured escorts (same as on escorts page)
        $featuredEscorts = \App\Models\Escort::with(['primaryImage', 'rates', 'locations', 'services'])
            ->featured()
            ->verified()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->latest()
            ->take(4)
            ->get();

        // Get premium agencies
        $premiumAgencies = Agency::with(['user', 'escorts'])
            ->verified()
            ->where('is_premium', true)
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->orderBy('name')
            ->take(6)
            ->get();

        // Get regular agencies
        $agencies = Agency::with(['user', 'escorts'])
            ->verified()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->orderBy('is_premium', 'desc')
            ->orderBy('name')
            ->paginate(12);

        // Generate slugs for agencies that don't have them
        $this->ensureAgencySlugs($premiumAgencies);
        $this->ensureAgencySlugs($agencies);

        // Generate SEO meta data
        $seoMeta = [
            'title' => 'Professional Escort Agencies in Uganda | Verified Premium Services',
            'description' => 'Find verified professional escort agencies in Uganda. Premium agencies with verified escorts, safe platform, and professional services in Kampala and across Uganda.',
            'keywords' => 'escort agencies Uganda, Kampala escort agencies, professional escort services, verified agencies Uganda, premium escort agencies',
            'type' => 'website'
        ];

        return view('agencies.index', compact('agencies', 'premiumAgencies', 'featuredEscorts', 'seoMeta'));
    }

    /**
     * Search for agencies.
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        $agencies = Agency::with(['user'])
            ->verified()
            ->approved()
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->orderBy('is_premium', 'desc')
            ->orderBy('name')
            ->paginate(12);

        return view('agencies.index', compact('agencies', 'query'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Try to find by slug first if it's not numeric
        if (!is_numeric($id)) {
            $agency = Agency::with(['escorts.locations', 'escorts.services', 'escorts.images', 'user'])
                ->where('slug', $id)
                ->verified()
                ->approved()
                ->first();

            if ($agency) {
                // Increment view count
                $agency->increment('profile_views');

                // Get featured escorts from this agency
                $featuredEscorts = $agency->escorts()
                    ->verified()
                    ->featured()
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    })
                    ->with(['locations', 'services', 'images'])
                    ->orderBy('name')
                    ->take(4)
                    ->get();

                // Get regular escorts from this agency
                $regularEscorts = $agency->escorts()
                    ->verified()
                    ->where('is_featured', false)
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    })
                    ->with(['locations', 'services', 'images'])
                    ->orderBy('name')
                    ->take(8)
                    ->get();

                // Get total escort count
                $totalEscorts = $agency->escorts()
                    ->verified()
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    })
                    ->count();

                // Generate SEO meta data and structured data
                $seoMeta = $this->seoService->getAgencyProfileMeta($agency);
                $structuredData = $this->seoService->getAgencyStructuredData($agency);

                return view('agencies.show', compact('agency', 'featuredEscorts', 'regularEscorts', 'totalEscorts', 'seoMeta', 'structuredData'));
            }
        }

        // If not found by slug or ID is numeric, find by ID
        $agency = Agency::with(['escorts.locations', 'escorts.services', 'escorts.images', 'user'])
            ->where('id', $id)
            ->verified()
            ->approved()
            ->active()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->firstOrFail();

        // Increment view count
        $agency->increment('profile_views');

        // Get featured escorts from this agency
        $featuredEscorts = $agency->escorts()
            ->verified()
            ->featured()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->with(['locations', 'services', 'images'])
            ->orderBy('is_verified', 'desc')
            ->orderBy('name')
            ->take(4)
            ->get();

        // Get regular escorts from this agency
        $regularEscorts = $agency->escorts()
            ->verified()
            ->where('is_featured', false)
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->with(['locations', 'services', 'images'])
            ->orderBy('is_verified', 'desc')
            ->orderBy('name')
            ->take(8)
            ->get();

        // Get total escort count (only active escorts)
        $totalEscorts = $agency->escorts()
            ->verified()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->count();

        // Generate SEO meta data and structured data
        $seoMeta = $this->seoService->getAgencyProfileMeta($agency);
        $structuredData = $this->seoService->getAgencyStructuredData($agency);

        return view('agencies.show', compact('agency', 'featuredEscorts', 'regularEscorts', 'totalEscorts', 'seoMeta', 'structuredData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Ensure all agencies in the collection have slugs
     *
     * @param Collection $agencies
     * @return void
     */
    protected function ensureAgencySlugs($agencies)
    {
        foreach ($agencies as $agency) {
            if (empty($agency->slug)) {
                $slug = Str::slug($agency->name);
                $originalSlug = $slug;
                $count = 1;

                // Make sure the slug is unique
                while (Agency::where('slug', $slug)->where('id', '!=', $agency->id)->exists()) {
                    $slug = $originalSlug . '-' . $count++;
                }

                $agency->slug = $slug;
                $agency->save();
            }
        }
    }
}
