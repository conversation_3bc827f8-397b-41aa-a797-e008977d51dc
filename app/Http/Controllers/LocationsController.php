<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Escort;
use App\Services\SeoService;
use Illuminate\Http\Request;

class LocationsController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display a listing of locations.
     */
    public function index()
    {
        try {
            // Get all countries with their cities and areas
            $countries = Location::active()
                ->ofType('country')
                ->orderBy('name')
                ->get();

            // Get cities for each country with their areas
            $countriesWithCities = [];
            foreach ($countries as $country) {
                $cities = Location::active()
                    ->ofType('city')
                    ->where('parent_id', $country->id)
                    ->orderBy('name')
                    ->get();

                $citiesWithAreas = [];
                foreach ($cities as $city) {
                    $areas = Location::active()
                        ->ofType('area')
                        ->where('parent_id', $city->id)
                        ->orderBy('name')
                        ->get();

                    $areaCount = Location::active()
                        ->ofType('area')
                        ->where('parent_id', $city->id)
                        ->count();

                    // Get escort count for this city (only active escorts)
                    $escortCount = Escort::whereHas('locations', function($query) use ($city) {
                        $query->where('location_id', $city->id);
                    })
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    })
                    ->count();

                    $citiesWithAreas[] = [
                        'city' => $city,
                        'areas' => $areas->take(6),
                        'area_count' => $areaCount,
                        'escort_count' => $escortCount
                    ];
                }

                // Only include countries that have cities with escorts
                if (!empty($citiesWithAreas)) {
                    $countriesWithCities[] = [
                        'country' => $country,
                        'cities' => $citiesWithAreas
                    ];
                }
            }

            // Generate SEO meta data
            $seoMeta = [
                'title' => 'Escort Locations in Uganda & East Africa | Find Escorts by City',
                'description' => 'Find professional escorts by location in Uganda and East Africa. Browse escorts in Kampala, Entebbe, Jinja, Nairobi, Dar es Salaam and other major cities.',
                'keywords' => 'escort locations Uganda, escorts by city, Kampala escorts, Entebbe escorts, East Africa escort locations, Uganda cities escorts',
                'type' => 'website'
            ];

            return view('locations.index', compact('countries', 'countriesWithCities', 'seoMeta'));
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), show a placeholder page
            $countries = collect();
            $countriesWithCities = [];

            return view('locations.index', compact('countries', 'countriesWithCities'))
                ->with('error', 'Locations are currently being updated. Please check back soon.');
        }
    }

    /**
     * Display escorts for a specific location.
     */
    public function show($slug, Request $request)
    {
        try {
            $location = Location::active()
                ->where('slug', $slug)
                ->firstOrFail();

            // Get all child location IDs (including the current one)
            $locationIds = $this->getAllChildLocationIds($location);

        // Get escorts in this location and all child locations with filters
        $escortsQuery = Escort::whereHas('locations', function($query) use ($locationIds) {
                $query->whereIn('locations.id', $locationIds);
            })
            ->verified()
            ->whereHas('user', function ($q) {
                $q->where('is_active', true);
            })
            ->with(['user', 'locations', 'rates', 'services', 'images']);

        // Apply filters if they exist
        if ($request->filled('gender')) {
            $escortsQuery->where('gender', $request->gender);
        }

        if ($request->filled('age')) {
            $ageRange = explode('-', $request->age);
            if (count($ageRange) == 2) {
                $escortsQuery->whereBetween('age', [$ageRange[0], $ageRange[1]]);
            } elseif (str_ends_with($request->age, '+')) {
                $minAge = (int) $request->age;
                $escortsQuery->where('age', '>=', $minAge);
            }
        }

        if ($request->filled('service')) {
            $escortsQuery->whereHas('services', function($query) use ($request) {
                $query->where('services.id', $request->service);
            });
        }

        // Apply sorting
        if ($request->filled('sort')) {
            switch ($request->sort) {
                case 'oldest':
                    $escortsQuery->oldest();
                    break;
                case 'price_low':
                    $escortsQuery->whereHas('rates', function($query) {
                        $query->orderBy('amount', 'asc');
                    });
                    break;
                case 'price_high':
                    $escortsQuery->whereHas('rates', function($query) {
                        $query->orderBy('amount', 'desc');
                    });
                    break;
                case 'rating':
                    $escortsQuery->orderBy('rating', 'desc');
                    break;
                default:
                    $escortsQuery->orderBy('is_verified', 'desc')->latest();
            }
        } else {
            $escortsQuery->orderBy('is_verified', 'desc')->latest();
        }

        $escorts = $escortsQuery->paginate(12)->appends($request->query());

        // Get child locations for filtering
        $childLocations = [];
        if ($location->type == 'country') {
            $childLocations = Location::active()
                ->ofType('city')
                ->where('parent_id', $location->id)
                ->orderBy('name')
                ->get();
        } elseif ($location->type == 'city') {
            $childLocations = Location::active()
                ->ofType('area')
                ->where('parent_id', $location->id)
                ->orderBy('name')
                ->get();
        }

        // Get popular services in this location
        $popularServices = \App\Models\Service::whereHas('escorts', function($query) use ($locationIds) {
                $query->whereHas('locations', function($locQuery) use ($locationIds) {
                    $locQuery->whereIn('locations.id', $locationIds);
                });
            })
            ->withCount(['escorts' => function($query) use ($locationIds) {
                $query->whereHas('locations', function($locQuery) use ($locationIds) {
                    $locQuery->whereIn('locations.id', $locationIds);
                });
            }])
            ->orderBy('escorts_count', 'desc')
            ->take(10)
            ->get();

        // Get upcoming tours to this location
        $upcomingTours = \App\Models\Tour::where('location_id', $location->id)
            ->where('start_date', '>=', now())
            ->with('escort')
            ->orderBy('start_date')
            ->take(5)
            ->get();

        // Get related locations (siblings or nearby)
        $relatedLocations = [];
        if ($location->parent_id) {
            // Get sibling locations (same parent)
            $relatedLocations = Location::active()
                ->where('parent_id', $location->parent_id)
                ->where('id', '!=', $location->id)
                ->take(5)
                ->get();
        } else {
            // For countries, get other countries
            $relatedLocations = Location::active()
                ->ofType($location->type)
                ->where('id', '!=', $location->id)
                ->take(5)
                ->get();
        }

            // Generate SEO meta data and content
            $escortCount = $escorts->total();
            $seoMeta = $this->seoService->getLocationPageMeta($location);
            $seoContent = $this->seoService->generateLocationSeoContent($location, $escortCount);
            $faqData = $this->seoService->generateLocationFaqData($location);

            return view('locations.show', compact(
                'location',
                'escorts',
                'childLocations',
                'popularServices',
                'upcomingTours',
                'relatedLocations',
                'seoMeta',
                'seoContent',
                'faqData'
            ));
        } catch (\Exception $e) {
            // If location doesn't exist or there's an error, redirect to locations index
            return redirect()->route('locations.index')
                ->with('error', 'The requested location could not be found.');
        }
    }

    /**
     * Get all child location IDs including the current location.
     */
    private function getAllChildLocationIds($location)
    {
        $ids = [$location->id];

        // If it's a country, include all cities and areas
        if ($location->type == 'country') {
            $cities = Location::active()
                ->ofType('city')
                ->where('parent_id', $location->id)
                ->get();

            foreach ($cities as $city) {
                $ids[] = $city->id;

                // Add areas within this city
                $areas = Location::active()
                    ->ofType('area')
                    ->where('parent_id', $city->id)
                    ->pluck('id')
                    ->toArray();

                $ids = array_merge($ids, $areas);
            }
        }
        // If it's a city, include all areas
        elseif ($location->type == 'city') {
            $areas = Location::active()
                ->ofType('area')
                ->where('parent_id', $location->id)
                ->pluck('id')
                ->toArray();

            $ids = array_merge($ids, $areas);
        }

        return $ids;
    }

    /**
     * Get locations for AJAX requests (used in forms).
     */
    public function getLocations(Request $request)
    {
        $type = $request->input('type', 'country');
        $parentId = $request->input('parent_id');

        $query = Location::active()->ofType($type);

        if ($parentId) {
            $query->where('parent_id', $parentId);
        } elseif ($type !== 'country') {
            // If no parent ID is provided and we're not looking for countries,
            // return an empty result
            return response()->json([]);
        }

        $locations = $query->orderBy('name')->get();

        return response()->json($locations);
    }
}