<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\EscortImage;

class PhotoController extends Controller
{
    /**
     * Display a listing of the escort's photos.
     */
    public function index()
    {
        $user = Auth::user();
        $escort = $user->escort;
        $images = $escort->images;

        return view('escort.photos.index', compact('escort', 'images'));
    }

    /**
     * Store a newly created photo in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 5MB max
            'is_main' => 'boolean',
        ]);

        $escort = Auth::user()->escort;

        // Handle file upload
        $path = $request->file('image')->store('escorts/' . $escort->id, 'public');

        // If this is set as main image, unset other main images
        if ($request->has('is_main') && $request->is_main) {
            $escort->images()->update(['is_main' => false]);
        }

        // Create image record
        $isMain = $request->has('is_main') && $request->is_main;

        $image = EscortImage::create([
            'escort_id' => $escort->id,
            'path' => $path,
            'image_path' => $path, // Ensure both path fields are populated for compatibility
            'is_main' => $isMain,
            'is_primary' => $isMain, // Set is_primary for compatibility
        ]);

        return redirect()->route('escort.photos')->with('success', 'Photo uploaded successfully.');
    }

    /**
     * Remove the specified photo from storage.
     */
    public function destroy($id)
    {
        $escort = Auth::user()->escort;
        $image = EscortImage::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        // Delete file from storage
        Storage::disk('public')->delete($image->path);

        // Delete record
        $image->delete();

        return redirect()->route('escort.photos')->with('success', 'Photo deleted successfully.');
    }

    /**
     * Set a photo as the main profile image.
     */
    public function setMain($id)
    {
        $escort = Auth::user()->escort;

        // Unset all main images
        $escort->images()->update([
            'is_main' => false,
            'is_primary' => false
        ]);

        // Set the selected image as main
        $image = EscortImage::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();
        $image->is_main = true;
        $image->is_primary = true;
        $image->save();

        return redirect()->route('escort.photos')->with('success', 'Main photo updated successfully.');
    }
}
