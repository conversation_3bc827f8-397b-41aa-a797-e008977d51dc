<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Location;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        try {
            // Get featured escorts for the home page
            $featuredEscorts = Escort::with(['primaryImage', 'rates', 'locations', 'services'])
                ->featured()
                ->verified()
                ->whereHas('user', function ($q) {
                    $q->where('is_active', true);
                })
                ->latest()
                ->take(4)
                ->get();

            // Get popular locations
            $popularLocations = Location::active()
                ->ofType('city')
                ->withCount('escorts')
                ->orderBy('escorts_count', 'desc')
                ->take(4)
                ->get();
        } catch (\Exception $e) {
            // Fallback data if there are database issues
            $featuredEscorts = collect();
            $popularLocations = collect();
        }

        return view('home', compact('featuredEscorts', 'popularLocations'));
    }
}
