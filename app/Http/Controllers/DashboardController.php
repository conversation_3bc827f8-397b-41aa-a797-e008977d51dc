<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\DashboardMetricsService;

class DashboardController extends Controller
{
    protected $metricsService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(DashboardMetricsService $metricsService)
    {
        $this->middleware('auth');
        $this->metricsService = $metricsService;
    }

    /**
     * Show the appropriate dashboard based on user type.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isAdmin()) {
            $metrics = $this->metricsService->getAdminMetrics();
            return view('dashboard.admin', compact('metrics'));
        } elseif ($user->isEscort()) {
            // Load escort with relationships needed for the dashboard
            $user->load(['escort.images']);
            $escort = $user->escort;
            $metrics = $this->metricsService->getEscortDashboardMetrics($escort);
            return view('dashboard.escort', compact('metrics'));
        } elseif ($user->isAgency()) {
            $agency = $user->agency;
            $metrics = $this->metricsService->getAgencyDashboardMetrics($agency);
            return view('dashboard.agency', compact('metrics'));
        } else {
            // Redirect to home if user type is not recognized
            return redirect()->route('home');
        }
    }
}
