<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Agency;
use App\Models\Location;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate main sitemap index
     */
    public function index(): Response
    {
        $sitemaps = [
            [
                'loc' => route('sitemap.pages'),
                'lastmod' => Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.escorts'),
                'lastmod' => Escort::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.agencies'),
                'lastmod' => Agency::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.locations'),
                'lastmod' => Location::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.images'),
                'lastmod' => Carbon::now()->toISOString(),
            ],
        ];

        return response()
            ->view('sitemaps.index', compact('sitemaps'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate static pages sitemap
     */
    public function pages(): Response
    {
        $pages = [
            [
                'loc' => route('home'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '1.0',
            ],
            [
                'loc' => route('escorts.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '1.0',
            ],
            [
                'loc' => route('agencies.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8',
            ],
            [
                'loc' => route('locations.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8',
            ],
            [
                'loc' => route('contact.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.5',
            ],
        ];

        return response()
            ->view('sitemaps.pages', compact('pages'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate escorts sitemap
     */
    public function escorts(): Response
    {
        $escorts = Escort::where('is_active', true)
            ->with(['locations'])
            ->get()
            ->map(function ($escort) {
                return [
                    'loc' => route('escorts.show', $escort->slug),
                    'lastmod' => $escort->updated_at->toISOString(),
                    'changefreq' => 'weekly',
                    'priority' => $escort->is_featured ? '0.9' : '0.7',
                ];
            });

        return response()
            ->view('sitemaps.escorts', compact('escorts'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate agencies sitemap
     */
    public function agencies(): Response
    {
        $agencies = Agency::where('is_approved', true)
            ->get()
            ->map(function ($agency) {
                return [
                    'loc' => route('agencies.show', $agency->slug),
                    'lastmod' => $agency->updated_at->toISOString(),
                    'changefreq' => 'weekly',
                    'priority' => '0.8',
                ];
            });

        return response()
            ->view('sitemaps.agencies', compact('agencies'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate locations sitemap
     */
    public function locations(): Response
    {
        $locations = Location::where('is_active', true)
            ->withCount('escorts')
            ->get()
            ->map(function ($location) {
                return [
                    'loc' => route('locations.show', $location->slug),
                    'lastmod' => $location->updated_at->toISOString(),
                    'changefreq' => 'weekly',
                    'priority' => $location->escorts_count > 10 ? '0.8' : '0.6',
                ];
            });

        return response()
            ->view('sitemaps.locations', compact('locations'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate images sitemap
     */
    public function images(): Response
    {
        $escorts = Escort::where('is_active', true)
            ->with(['images', 'locations'])
            ->get();

        $images = [];
        
        foreach ($escorts as $escort) {
            foreach ($escort->images as $image) {
                $images[] = [
                    'loc' => route('escorts.show', $escort->slug),
                    'image_loc' => asset('storage/' . $image->image_path),
                    'image_caption' => $escort->name . ' - Professional Escort in ' . ($escort->locations->first()?->name ?? 'Uganda'),
                    'image_title' => $escort->name,
                ];
            }
        }

        return response()
            ->view('sitemaps.images', compact('images'))
            ->header('Content-Type', 'application/xml');
    }
}
