<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Agency;
use App\Models\Location;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate main sitemap index
     */
    public function index(): Response
    {
        $sitemaps = [
            [
                'loc' => route('sitemap.pages'),
                'lastmod' => Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.escorts'),
                'lastmod' => Escort::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.agencies'),
                'lastmod' => Agency::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.locations'),
                'lastmod' => Location::latest('updated_at')->first()?->updated_at?->toISOString() ?? Carbon::now()->toISOString(),
            ],
            [
                'loc' => route('sitemap.images'),
                'lastmod' => Carbon::now()->toISOString(),
            ],
        ];

        return response()
            ->view('sitemaps.index', compact('sitemaps'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate static pages sitemap
     */
    public function pages(): Response
    {
        $pages = [
            [
                'loc' => route('home'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '1.0',
            ],
            [
                'loc' => route('escorts.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '1.0',
            ],
            [
                'loc' => route('agencies.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8',
            ],
            [
                'loc' => route('locations.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8',
            ],
            [
                'loc' => route('contact.index'),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.5',
            ],
        ];

        return response()
            ->view('sitemaps.pages', compact('pages'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate static pages sitemap (alias for pages)
     */
    public function static(): Response
    {
        return $this->pages();
    }

    /**
     * Generate escorts sitemap
     */
    public function escorts(): Response
    {
        $escorts = Escort::where('is_active', true)
            ->where('is_verified', true) // Only include verified escorts
            ->with(['locations', 'images'])
            ->get()
            ->map(function ($escort) {
                // Higher priority for featured and verified escorts
                $priority = '0.7';
                if ($escort->is_featured && $escort->is_verified) {
                    $priority = '0.9';
                } elseif ($escort->is_featured || $escort->is_verified) {
                    $priority = '0.8';
                }

                return [
                    'loc' => route('escorts.show', $escort->slug),
                    'lastmod' => $escort->updated_at->toISOString(),
                    'changefreq' => $escort->is_featured ? 'daily' : 'weekly',
                    'priority' => $priority,
                ];
            });

        return response()
            ->view('sitemaps.escorts', compact('escorts'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate agencies sitemap
     */
    public function agencies(): Response
    {
        $agencies = Agency::where('is_approved', true)
            ->where('is_verified', true) // Only include verified agencies
            ->withCount('escorts')
            ->get()
            ->map(function ($agency) {
                // Higher priority for agencies with more escorts
                $priority = '0.7';
                if ($agency->escorts_count >= 10) {
                    $priority = '0.9';
                } elseif ($agency->escorts_count >= 5) {
                    $priority = '0.8';
                }

                return [
                    'loc' => route('agencies.show', $agency->slug),
                    'lastmod' => $agency->updated_at->toISOString(),
                    'changefreq' => $agency->escorts_count >= 5 ? 'daily' : 'weekly',
                    'priority' => $priority,
                ];
            });

        return response()
            ->view('sitemaps.agencies', compact('agencies'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate locations sitemap
     */
    public function locations(): Response
    {
        $locations = Location::where('is_active', true)
            ->withCount('escorts')
            ->get()
            ->map(function ($location) {
                return [
                    'loc' => route('locations.show', $location->slug),
                    'lastmod' => $location->updated_at->toISOString(),
                    'changefreq' => 'weekly',
                    'priority' => $location->escorts_count > 10 ? '0.8' : '0.6',
                ];
            });

        return response()
            ->view('sitemaps.locations', compact('locations'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate images sitemap
     */
    public function images(): Response
    {
        $escorts = Escort::where('is_active', true)
            ->where('is_verified', true) // Only include verified escorts
            ->with(['images', 'locations'])
            ->get();

        $images = [];

        foreach ($escorts as $escort) {
            foreach ($escort->images as $index => $image) {
                $location = $escort->locations->first()?->name ?? 'Uganda';
                $images[] = [
                    'loc' => route('escorts.show', $escort->slug),
                    'image_loc' => asset('storage/' . $image->image_path),
                    'image_caption' => $escort->stage_name . ' - Professional Escort in ' . $location .
                                     ($escort->is_verified ? ' (Verified)' : ''),
                    'image_title' => $escort->stage_name . ' - ' . $location . ' Escort',
                    'image_geo_location' => $location . ', Uganda',
                ];
            }
        }

        return response()
            ->view('sitemaps.images', compact('images'))
            ->header('Content-Type', 'application/xml');
    }
}
