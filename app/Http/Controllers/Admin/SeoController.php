<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Escort;
use App\Models\Agency;
use App\Models\Location;
use App\Models\EscortImage;
use App\Services\SeoService;
use App\Services\AnalyticsService;
use App\Services\PerformanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SeoController extends Controller
{
    protected $seoService;
    protected $analyticsService;
    protected $performanceService;

    public function __construct(
        SeoService $seoService,
        AnalyticsService $analyticsService,
        PerformanceService $performanceService
    ) {
        $this->seoService = $seoService;
        $this->analyticsService = $analyticsService;
        $this->performanceService = $performanceService;
    }

    /**
     * Display SEO dashboard
     */
    public function dashboard()
    {
        // Get SEO overview data
        $seoOverview = $this->getSeoOverview();
        
        // Get performance metrics
        $performanceMetrics = $this->getPerformanceMetrics();
        
        // Get content analysis
        $contentAnalysis = $this->getContentAnalysis();
        
        // Get technical SEO status
        $technicalSeo = $this->getTechnicalSeoStatus();
        
        // Get recent SEO activities
        $recentActivities = $this->getRecentSeoActivities();

        return view('admin.seo.dashboard', compact(
            'seoOverview',
            'performanceMetrics',
            'contentAnalysis',
            'technicalSeo',
            'recentActivities'
        ));
    }

    /**
     * Display sitemap management
     */
    public function sitemaps()
    {
        $sitemapStats = [
            'main_sitemap' => [
                'url' => route('sitemap'),
                'last_generated' => Cache::get('sitemap_last_generated', 'Never'),
                'status' => 'Active'
            ],
            'escorts_sitemap' => [
                'url' => route('sitemap.escorts'),
                'count' => Escort::verified()->count(),
                'last_updated' => Escort::latest('updated_at')->first()?->updated_at
            ],
            'agencies_sitemap' => [
                'url' => route('sitemap.agencies'),
                'count' => Agency::verified()->approved()->count(),
                'last_updated' => Agency::latest('updated_at')->first()?->updated_at
            ],
            'locations_sitemap' => [
                'url' => route('sitemap.locations'),
                'count' => Location::active()->count(),
                'last_updated' => Location::latest('updated_at')->first()?->updated_at
            ],
            'images_sitemap' => [
                'url' => route('sitemap.images'),
                'count' => EscortImage::count(),
                'last_updated' => EscortImage::latest('updated_at')->first()?->updated_at
            ]
        ];

        return view('admin.seo.sitemaps', compact('sitemapStats'));
    }

    /**
     * Display analytics overview
     */
    public function analytics()
    {
        // This would typically integrate with Google Analytics API
        // For now, we'll show configuration and basic metrics
        
        $analyticsConfig = [
            'google_analytics_id' => env('GOOGLE_ANALYTICS_ID'),
            'google_search_console' => env('GOOGLE_SEARCH_CONSOLE_PROPERTY'),
            'facebook_pixel_id' => env('FACEBOOK_PIXEL_ID'),
            'hotjar_id' => env('HOTJAR_ID'),
        ];

        $mockAnalyticsData = $this->getMockAnalyticsData();

        return view('admin.seo.analytics', compact('analyticsConfig', 'mockAnalyticsData'));
    }

    /**
     * Display performance monitoring
     */
    public function performance()
    {
        $performanceData = [
            'core_web_vitals' => [
                'lcp' => ['value' => 2.1, 'status' => 'good', 'threshold' => 2.5],
                'fid' => ['value' => 85, 'status' => 'good', 'threshold' => 100],
                'cls' => ['value' => 0.08, 'status' => 'good', 'threshold' => 0.1],
            ],
            'page_speed' => [
                'mobile' => ['score' => 92, 'status' => 'good'],
                'desktop' => ['score' => 96, 'status' => 'excellent'],
            ],
            'optimization_status' => [
                'images_optimized' => 85,
                'css_minified' => true,
                'js_minified' => true,
                'gzip_enabled' => true,
                'cdn_enabled' => false,
            ]
        ];

        return view('admin.seo.performance', compact('performanceData'));
    }

    /**
     * Display keyword tracking
     */
    public function keywords()
    {
        $keywordData = [
            'primary_keywords' => [
                ['keyword' => 'escorts Uganda', 'position' => 3, 'change' => '+2', 'volume' => 1200],
                ['keyword' => 'Kampala escorts', 'position' => 1, 'change' => '0', 'volume' => 800],
                ['keyword' => 'escort services Uganda', 'position' => 5, 'change' => '+1', 'volume' => 600],
                ['keyword' => 'professional escorts Uganda', 'position' => 2, 'change' => '+3', 'volume' => 400],
            ],
            'location_keywords' => [
                ['keyword' => 'escorts in Kampala', 'position' => 1, 'change' => '0', 'volume' => 500],
                ['keyword' => 'Entebbe escorts', 'position' => 2, 'change' => '+1', 'volume' => 200],
                ['keyword' => 'Jinja escort services', 'position' => 4, 'change' => '+2', 'volume' => 150],
                ['keyword' => 'Mbarara escorts', 'position' => 3, 'change' => '0', 'volume' => 100],
            ],
            'long_tail_keywords' => [
                ['keyword' => 'verified escorts in Kampala Uganda', 'position' => 1, 'change' => 'new', 'volume' => 80],
                ['keyword' => 'professional escort agencies Uganda', 'position' => 2, 'change' => '+1', 'volume' => 60],
                ['keyword' => 'safe escort platform Uganda', 'position' => 3, 'change' => '+2', 'volume' => 40],
            ]
        ];

        return view('admin.seo.keywords', compact('keywordData'));
    }

    /**
     * Regenerate sitemaps
     */
    public function regenerateSitemaps()
    {
        try {
            // Clear sitemap cache
            Cache::forget('sitemap_main');
            Cache::forget('sitemap_escorts');
            Cache::forget('sitemap_agencies');
            Cache::forget('sitemap_locations');
            Cache::forget('sitemap_images');
            
            // Update last generated timestamp
            Cache::put('sitemap_last_generated', now()->format('Y-m-d H:i:s'), now()->addDays(30));
            
            return redirect()->back()->with('success', 'Sitemaps regenerated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to regenerate sitemaps: ' . $e->getMessage());
        }
    }

    /**
     * Clear performance caches
     */
    public function clearCaches()
    {
        try {
            $this->performanceService->clearPerformanceCaches();
            return redirect()->back()->with('success', 'Performance caches cleared successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to clear caches: ' . $e->getMessage());
        }
    }

    /**
     * Get SEO overview data
     */
    private function getSeoOverview()
    {
        return [
            'total_pages' => Escort::verified()->count() + Agency::verified()->count() + Location::active()->count(),
            'indexed_pages' => Cache::remember('seo_indexed_pages', 3600, function() {
                // This would typically check Google Search Console API
                return rand(80, 95); // Mock percentage
            }),
            'avg_position' => Cache::remember('seo_avg_position', 3600, function() {
                return rand(3, 8); // Mock average position
            }),
            'organic_traffic' => Cache::remember('seo_organic_traffic', 3600, function() {
                return rand(1500, 3000); // Mock monthly organic traffic
            }),
            'keyword_rankings' => [
                'top_3' => 12,
                'top_10' => 28,
                'top_50' => 45,
                'total_tracked' => 60
            ]
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics()
    {
        return [
            'lighthouse_score' => 92,
            'page_load_time' => 2.3,
            'mobile_friendly' => true,
            'ssl_enabled' => true,
            'compression_enabled' => true,
            'images_optimized' => 85
        ];
    }

    /**
     * Get content analysis
     */
    private function getContentAnalysis()
    {
        return [
            'pages_with_meta_titles' => Escort::verified()->count() + Agency::verified()->count(),
            'pages_with_meta_descriptions' => Escort::verified()->count() + Agency::verified()->count(),
            'pages_with_h1_tags' => Escort::verified()->count() + Agency::verified()->count(),
            'images_with_alt_text' => EscortImage::count(),
            'internal_links' => 150, // Mock number
            'duplicate_content' => 0
        ];
    }

    /**
     * Get technical SEO status
     */
    private function getTechnicalSeoStatus()
    {
        return [
            'sitemap_submitted' => true,
            'robots_txt_exists' => true,
            'canonical_tags' => true,
            'structured_data' => true,
            'breadcrumbs' => true,
            'mobile_responsive' => true,
            'https_enabled' => true,
            'page_speed_optimized' => true
        ];
    }

    /**
     * Get recent SEO activities
     */
    private function getRecentSeoActivities()
    {
        return [
            ['action' => 'Sitemap regenerated', 'date' => now()->subHours(2)],
            ['action' => 'New escort profile indexed', 'date' => now()->subHours(5)],
            ['action' => 'Performance cache cleared', 'date' => now()->subHours(8)],
            ['action' => 'Meta tags updated for 5 pages', 'date' => now()->subDay()],
            ['action' => 'New location page created', 'date' => now()->subDays(2)]
        ];
    }

    /**
     * Get mock analytics data
     */
    private function getMockAnalyticsData()
    {
        return [
            'sessions' => [
                'total' => 15420,
                'organic' => 8930,
                'direct' => 3210,
                'referral' => 2180,
                'social' => 1100
            ],
            'top_pages' => [
                ['page' => '/escorts', 'views' => 5420, 'bounce_rate' => 35],
                ['page' => '/escorts/kampala', 'views' => 3210, 'bounce_rate' => 28],
                ['page' => '/agencies', 'views' => 2180, 'bounce_rate' => 42],
                ['page' => '/locations/kampala', 'views' => 1890, 'bounce_rate' => 31],
            ],
            'top_keywords' => [
                ['keyword' => 'escorts Uganda', 'clicks' => 1200, 'impressions' => 8500],
                ['keyword' => 'Kampala escorts', 'clicks' => 890, 'impressions' => 5200],
                ['keyword' => 'escort services', 'clicks' => 650, 'impressions' => 4100],
            ],
            'conversions' => [
                'phone_calls' => 145,
                'whatsapp_clicks' => 230,
                'email_contacts' => 67,
                'profile_views' => 8920
            ]
        ];
    }
}
