<?php

namespace App\Services;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SeoService
{
    /**
     * Generate SEO meta tags for a page
     */
    public function generateMetaTags(array $data = []): array
    {
        $defaults = [
            'title' => $this->getDefaultTitle(),
            'description' => $this->getDefaultDescription(),
            'keywords' => $this->getDefaultKeywords(),
            'image' => $this->getDefaultImage(),
            'url' => URL::current(),
            'type' => 'website',
            'site_name' => setting('site_name', 'Get Hot Babes'),
            'locale' => 'en_UG', // Uganda English
            'canonical' => URL::current(),
        ];

        $meta = array_merge($defaults, $data);

        // Ensure title is within SEO limits
        $meta['title'] = $this->optimizeTitle($meta['title']);

        // Ensure description is within SEO limits
        $meta['description'] = $this->optimizeDescription($meta['description']);

        return $meta;
    }

    /**
     * Generate title for escorts listing page
     */
    public function getEscortsPageMeta(string $location = null): array
    {
        if ($location) {
            return [
                'title' => "Professional Escorts in {$location}, Uganda | Verified Companions",
                'description' => "Find verified professional escorts and companions in {$location}, Uganda. Premium escort services with verified profiles, safe platform, and discreet arrangements.",
                'keywords' => "escorts {$location}, {$location} escorts, professional companions {$location}, verified escorts Uganda, escort services {$location}",
                'type' => 'website'
            ];
        }

        return [
            'title' => 'Professional Escorts in Uganda | Verified Companions & Premium Services',
            'description' => 'Find verified professional escorts and companions in Uganda. Premium escort services in Kampala, Entebbe, Jinja. Safe platform with verified profiles.',
            'keywords' => 'escorts Uganda, Kampala escorts, professional companions, verified escorts, escort services Uganda, premium escorts',
            'type' => 'website'
        ];
    }

    /**
     * Generate title for escort profile page
     */
    public function getEscortProfileMeta($escort): array
    {
        $location = $escort->locations->first()?->name ?? 'Uganda';
        $age = $escort->date_of_birth ? now()->diffInYears($escort->date_of_birth) : null;
        $ageText = $age ? ", {$age} years" : '';

        return [
            'title' => "{$escort->name} - Professional Escort in {$location}{$ageText} | Get Hot Babes",
            'description' => "Meet {$escort->name}, a verified professional escort in {$location}, Uganda. {$this->truncateText($escort->about ?? 'Professional companion services available.', 120)}",
            'keywords' => "escort {$escort->name}, {$location} escort, professional companion {$location}, verified escort Uganda",
            'image' => $escort->primaryImage?->image_path ? asset('storage/' . $escort->primaryImage->image_path) : null,
            'type' => 'profile'
        ];
    }

    /**
     * Generate meta for agency page
     */
    public function getAgencyProfileMeta($agency): array
    {
        $location = $agency->locations->first()?->name ?? 'Uganda';

        return [
            'title' => "{$agency->name} - Premium Escort Agency in {$location} | Get Hot Babes",
            'description' => "Professional escort agency {$agency->name} in {$location}, Uganda. Verified escorts, premium services, and professional companions available.",
            'keywords' => "escort agency {$agency->name}, {$location} escort agency, professional escort services, verified escorts {$location}",
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
            'type' => 'organization'
        ];
    }

    /**
     * Generate meta for location page
     */
    public function getLocationPageMeta($location): array
    {
        $escortCount = $location->escorts()->count();

        return [
            'title' => "Escorts in {$location->name}, Uganda | {$escortCount}+ Verified Companions",
            'description' => "Find {$escortCount}+ verified professional escorts and companions in {$location->name}, Uganda. Premium escort services, safe platform, verified profiles.",
            'keywords' => "escorts {$location->name}, {$location->name} escorts, professional companions {$location->name}, verified escorts {$location->name}, escort services {$location->name}",
            'type' => 'website'
        ];
    }

    /**
     * Generate structured data (JSON-LD) for escort profile
     */
    public function getEscortStructuredData($escort): array
    {
        $location = $escort->locations->first();

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => $escort->name,
            'description' => $this->truncateText($escort->about ?? 'Professional companion', 200),
            'image' => $escort->primaryImage?->image_path ? asset('storage/' . $escort->primaryImage->image_path) : null,
            'url' => route('escorts.show', $escort->slug),
            'address' => $location ? [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressCountry' => 'UG'
            ] : null,
            'offers' => $escort->rates->map(function($rate) {
                return [
                    '@type' => 'Offer',
                    'description' => $rate->duration . ' service',
                    'priceCurrency' => $rate->currency,
                    'price' => $rate->min_incall_price ?? $rate->incall_price,
                ];
            })->toArray()
        ];
    }

    /**
     * Generate structured data for agency
     */
    public function getAgencyStructuredData($agency): array
    {
        $location = $agency->locations->first();

        return [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $agency->name,
            'description' => $this->truncateText($agency->description ?? 'Professional escort agency', 200),
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
            'url' => route('agencies.show', $agency->slug),
            'address' => $location ? [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressCountry' => 'UG'
            ] : null,
            'telephone' => $agency->phone_number,
            'email' => $agency->email,
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function getBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $items = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        ];
    }

    /**
     * Get default title
     */
    private function getDefaultTitle(): string
    {
        return 'Professional Escorts in Uganda | Verified Companions | Get Hot Babes';
    }

    /**
     * Get default description
     */
    private function getDefaultDescription(): string
    {
        return 'Find verified professional escorts and companions in Uganda. Premium escort services in Kampala, Entebbe, Jinja, Mbarara and across East Africa. Safe, verified platform with discreet arrangements.';
    }

    /**
     * Get default keywords
     */
    private function getDefaultKeywords(): string
    {
        return 'escorts Uganda, Kampala escorts, professional companions, verified escorts, escort services Uganda, premium escorts, Entebbe escorts, Jinja escorts, Mbarara escorts, East Africa escorts, Uganda companions';
    }

    /**
     * Get default image
     */
    private function getDefaultImage(): string
    {
        return asset('images/og-default.jpg'); // We'll create this
    }

    /**
     * Optimize title for SEO (max 60 characters)
     */
    private function optimizeTitle(string $title): string
    {
        return Str::limit($title, 60, '...');
    }

    /**
     * Optimize description for SEO (max 160 characters)
     */
    private function optimizeDescription(string $description): string
    {
        return Str::limit($description, 160, '...');
    }

    /**
     * Truncate text to specified length
     */
    private function truncateText(string $text, int $length): string
    {
        return Str::limit(strip_tags($text), $length, '...');
    }

    /**
     * Generate SEO-optimized content for escort profiles
     */
    public function generateEscortSeoContent($escort): array
    {
        $location = $escort->locations->first()?->name ?? 'Uganda';
        $age = $escort->date_of_birth ? now()->diffInYears($escort->date_of_birth) : null;
        $services = $escort->services->pluck('name')->take(3)->join(', ');

        return [
            'h1' => "{$escort->name} - Professional Escort in {$location}",
            'h2_services' => "Premium Services by {$escort->name}",
            'h2_rates' => "Rates & Availability in {$location}",
            'h2_contact' => "Contact {$escort->name} - Verified Escort",
            'intro_text' => "Meet {$escort->name}, a verified professional escort offering premium companion services in {$location}, Uganda. " .
                           ($age ? "At {$age} years old, " : "") .
                           "{$escort->name} provides discreet, professional services including {$services}.",
            'location_text' => "Available in {$location} and surrounding areas for both incall and outcall appointments.",
            'verification_text' => $escort->is_verified ?
                "✓ Verified Profile - This escort has been verified by our team for authenticity and professionalism." :
                "Profile pending verification - Contact for more information.",
        ];
    }

    /**
     * Generate local SEO content for location pages
     */
    public function generateLocationSeoContent($location, $escortCount): array
    {
        $parentLocation = $location->parent?->name ?? 'Uganda';

        return [
            'h1' => "Professional Escorts in {$location->name}, {$parentLocation}",
            'h2_featured' => "Featured Escorts in {$location->name}",
            'h2_services' => "Popular Services in {$location->name}",
            'h2_areas' => $location->type === 'city' ? "Areas in {$location->name}" : "Cities in {$location->name}",
            'intro_text' => "Discover {$escortCount}+ verified professional escorts and companions in {$location->name}, {$parentLocation}. " .
                           "Our platform connects you with verified, professional escorts offering premium services in {$location->name}.",
            'location_description' => "Find the best escort services in {$location->name} with our comprehensive directory. " .
                                    "All escorts are verified for authenticity and professionalism, ensuring safe and discreet encounters.",
            'safety_text' => "All escorts in {$location->name} are verified through our strict verification process. " .
                           "We prioritize safety, discretion, and professionalism in all our listings.",
        ];
    }

    /**
     * Generate internal linking suggestions
     */
    public function generateInternalLinks($currentPage, $relatedItems = []): array
    {
        $links = [];

        switch ($currentPage) {
            case 'escort_profile':
                $links = [
                    'More escorts in this location',
                    'Similar escort services',
                    'Escort agencies in this area',
                    'Location guide and information'
                ];
                break;

            case 'location_page':
                $links = [
                    'Featured escorts in this location',
                    'Nearby locations and areas',
                    'Popular services in this area',
                    'Escort agencies serving this location'
                ];
                break;

            case 'agency_profile':
                $links = [
                    'All escorts from this agency',
                    'Other agencies in this location',
                    'Location information and guides',
                    'Similar premium agencies'
                ];
                break;
        }

        return $links;
    }

    /**
     * Generate alt text for escort images
     */
    public function generateImageAltText($escort, $imageIndex = 0): string
    {
        $location = $escort->locations->first()?->name ?? 'Uganda';
        $baseAlt = "{$escort->name} - Professional Escort in {$location}";

        if ($imageIndex > 0) {
            $baseAlt .= " - Photo " . ($imageIndex + 1);
        }

        if ($escort->is_verified) {
            $baseAlt .= " (Verified)";
        }

        return $baseAlt;
    }

    /**
     * Generate FAQ structured data for location pages
     */
    public function generateLocationFaqData($location): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => [
                [
                    '@type' => 'Question',
                    'name' => "How many escorts are available in {$location->name}?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "We have verified professional escorts available in {$location->name}. All profiles are verified for authenticity and safety."
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => "Are the escorts in {$location->name} verified?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "Yes, all escorts listed in {$location->name} go through our strict verification process to ensure authenticity and professionalism."
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => "What services are available in {$location->name}?",
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => "Professional escorts in {$location->name} offer various companion services including dinner dates, social events, and professional companionship."
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate local business structured data for agencies
     */
    public function generateLocalBusinessData($agency): array
    {
        $location = $agency->locations->first();

        $data = [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $agency->name,
            'description' => $this->truncateText($agency->description ?? 'Professional escort agency', 200),
            'url' => route('agencies.show', $agency->slug),
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
        ];

        if ($location) {
            $data['address'] = [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressRegion' => $location->parent?->name ?? 'Uganda',
                'addressCountry' => 'UG'
            ];

            $data['areaServed'] = [
                '@type' => 'City',
                'name' => $location->name
            ];
        }

        if ($agency->phone_number) {
            $data['telephone'] = $agency->phone_number;
        }

        if ($agency->email) {
            $data['email'] = $agency->email;
        }

        return $data;
    }

    /**
     * Alias for generateLocalBusinessData for backward compatibility
     */
    public function getLocalBusinessData($agency): array
    {
        return $this->generateLocalBusinessData($agency);
    }



    /**
     * Generate Open Graph meta tags
     */
    public function generateOpenGraphTags(array $meta): array
    {
        return [
            'og:title' => $meta['title'],
            'og:description' => $meta['description'],
            'og:type' => $meta['type'] ?? 'website',
            'og:url' => $meta['url'] ?? URL::current(),
            'og:image' => $meta['image'] ?? $this->getDefaultImage(),
            'og:site_name' => $meta['site_name'] ?? 'Get Hot Babes',
            'og:locale' => $meta['locale'] ?? 'en_UG',
        ];
    }

    /**
     * Generate Twitter Card meta tags
     */
    public function generateTwitterCardTags(array $meta): array
    {
        return [
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $meta['title'],
            'twitter:description' => $meta['description'],
            'twitter:image' => $meta['image'] ?? $this->getDefaultImage(),
            'twitter:image:alt' => $meta['title'],
        ];
    }

    /**
     * Generate hreflang tags for international SEO
     */
    public function generateHreflangTags(): array
    {
        $currentUrl = URL::current();

        return [
            'en-ug' => $currentUrl, // Uganda English
            'en-ke' => $currentUrl, // Kenya English
            'en-tz' => $currentUrl, // Tanzania English
            'en-rw' => $currentUrl, // Rwanda English
            'en' => $currentUrl,    // Default English
        ];
    }

    /**
     * Generate robots meta tag based on content type
     */
    public function generateRobotsTag($contentType = 'public'): string
    {
        switch ($contentType) {
            case 'escort_profile':
            case 'agency_profile':
            case 'location_page':
                return 'index, follow, max-image-preview:large';
            case 'private':
                return 'noindex, nofollow';
            case 'admin':
                return 'noindex, nofollow, noarchive, nosnippet';
            default:
                return 'index, follow';
        }
    }
}
