<?php

namespace App\Services;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SeoService
{
    /**
     * Generate SEO meta tags for a page
     */
    public function generateMetaTags(array $data = []): array
    {
        $defaults = [
            'title' => $this->getDefaultTitle(),
            'description' => $this->getDefaultDescription(),
            'keywords' => $this->getDefaultKeywords(),
            'image' => $this->getDefaultImage(),
            'url' => URL::current(),
            'type' => 'website',
            'site_name' => setting('site_name', 'Get Hot Babes'),
            'locale' => 'en_UG', // Uganda English
            'canonical' => URL::current(),
        ];

        $meta = array_merge($defaults, $data);

        // Ensure title is within SEO limits
        $meta['title'] = $this->optimizeTitle($meta['title']);
        
        // Ensure description is within SEO limits
        $meta['description'] = $this->optimizeDescription($meta['description']);

        return $meta;
    }

    /**
     * Generate title for escorts listing page
     */
    public function getEscortsPageMeta(string $location = null): array
    {
        if ($location) {
            return [
                'title' => "Professional Escorts in {$location}, Uganda | Verified Companions",
                'description' => "Find verified professional escorts and companions in {$location}, Uganda. Premium escort services with verified profiles, safe platform, and discreet arrangements.",
                'keywords' => "escorts {$location}, {$location} escorts, professional companions {$location}, verified escorts Uganda, escort services {$location}",
                'type' => 'website'
            ];
        }

        return [
            'title' => 'Professional Escorts in Uganda | Verified Companions & Premium Services',
            'description' => 'Find verified professional escorts and companions in Uganda. Premium escort services in Kampala, Entebbe, Jinja. Safe platform with verified profiles.',
            'keywords' => 'escorts Uganda, Kampala escorts, professional companions, verified escorts, escort services Uganda, premium escorts',
            'type' => 'website'
        ];
    }

    /**
     * Generate title for escort profile page
     */
    public function getEscortProfileMeta($escort): array
    {
        $location = $escort->locations->first()?->name ?? 'Uganda';
        $age = $escort->date_of_birth ? now()->diffInYears($escort->date_of_birth) : null;
        $ageText = $age ? ", {$age} years" : '';

        return [
            'title' => "{$escort->name} - Professional Escort in {$location}{$ageText} | Get Hot Babes",
            'description' => "Meet {$escort->name}, a verified professional escort in {$location}, Uganda. {$this->truncateText($escort->about ?? 'Professional companion services available.', 120)}",
            'keywords' => "escort {$escort->name}, {$location} escort, professional companion {$location}, verified escort Uganda",
            'image' => $escort->primaryImage?->image_path ? asset('storage/' . $escort->primaryImage->image_path) : null,
            'type' => 'profile'
        ];
    }

    /**
     * Generate meta for agency page
     */
    public function getAgencyProfileMeta($agency): array
    {
        $location = $agency->locations->first()?->name ?? 'Uganda';

        return [
            'title' => "{$agency->name} - Premium Escort Agency in {$location} | Get Hot Babes",
            'description' => "Professional escort agency {$agency->name} in {$location}, Uganda. Verified escorts, premium services, and professional companions available.",
            'keywords' => "escort agency {$agency->name}, {$location} escort agency, professional escort services, verified escorts {$location}",
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
            'type' => 'organization'
        ];
    }

    /**
     * Generate meta for location page
     */
    public function getLocationPageMeta($location): array
    {
        $escortCount = $location->escorts()->count();

        return [
            'title' => "Escorts in {$location->name}, Uganda | {$escortCount}+ Verified Companions",
            'description' => "Find {$escortCount}+ verified professional escorts and companions in {$location->name}, Uganda. Premium escort services, safe platform, verified profiles.",
            'keywords' => "escorts {$location->name}, {$location->name} escorts, professional companions {$location->name}, verified escorts {$location->name}, escort services {$location->name}",
            'type' => 'website'
        ];
    }

    /**
     * Generate structured data (JSON-LD) for escort profile
     */
    public function getEscortStructuredData($escort): array
    {
        $location = $escort->locations->first();
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => $escort->name,
            'description' => $this->truncateText($escort->about ?? 'Professional companion', 200),
            'image' => $escort->primaryImage?->image_path ? asset('storage/' . $escort->primaryImage->image_path) : null,
            'url' => route('escorts.show', $escort->slug),
            'address' => $location ? [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressCountry' => 'UG'
            ] : null,
            'offers' => $escort->rates->map(function($rate) {
                return [
                    '@type' => 'Offer',
                    'description' => $rate->duration . ' service',
                    'priceCurrency' => $rate->currency,
                    'price' => $rate->min_incall_price ?? $rate->incall_price,
                ];
            })->toArray()
        ];
    }

    /**
     * Generate structured data for agency
     */
    public function getAgencyStructuredData($agency): array
    {
        $location = $agency->locations->first();

        return [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $agency->name,
            'description' => $this->truncateText($agency->description ?? 'Professional escort agency', 200),
            'image' => $agency->logo ? asset('storage/' . $agency->logo) : null,
            'url' => route('agencies.show', $agency->slug),
            'address' => $location ? [
                '@type' => 'PostalAddress',
                'addressLocality' => $location->name,
                'addressCountry' => 'UG'
            ] : null,
            'telephone' => $agency->phone_number,
            'email' => $agency->email,
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function getBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $items = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        ];
    }

    /**
     * Get default title
     */
    private function getDefaultTitle(): string
    {
        return 'Professional Escorts in Uganda | Verified Companions | Get Hot Babes';
    }

    /**
     * Get default description
     */
    private function getDefaultDescription(): string
    {
        return 'Find verified professional escorts and companions in Uganda. Premium escort services in Kampala, Entebbe, Jinja and across East Africa. Safe, verified platform.';
    }

    /**
     * Get default keywords
     */
    private function getDefaultKeywords(): string
    {
        return 'escorts Uganda, Kampala escorts, professional companions, verified escorts, escort services Uganda, premium escorts, East Africa escorts';
    }

    /**
     * Get default image
     */
    private function getDefaultImage(): string
    {
        return asset('images/og-default.jpg'); // We'll create this
    }

    /**
     * Optimize title for SEO (max 60 characters)
     */
    private function optimizeTitle(string $title): string
    {
        return Str::limit($title, 60, '...');
    }

    /**
     * Optimize description for SEO (max 160 characters)
     */
    private function optimizeDescription(string $description): string
    {
        return Str::limit($description, 160, '...');
    }

    /**
     * Truncate text to specified length
     */
    private function truncateText(string $text, int $length): string
    {
        return Str::limit(strip_tags($text), $length, '...');
    }
}
