<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class PerformanceService
{
    /**
     * Optimize image for web delivery
     */
    public function optimizeImage(string $imagePath, array $sizes = []): array
    {
        $optimizedImages = [];
        $originalPath = storage_path('app/public/' . $imagePath);
        
        if (!file_exists($originalPath)) {
            return $optimizedImages;
        }

        // Default sizes for responsive images
        $defaultSizes = [
            'thumbnail' => ['width' => 150, 'height' => 150, 'quality' => 80],
            'small' => ['width' => 300, 'height' => 300, 'quality' => 85],
            'medium' => ['width' => 600, 'height' => 600, 'quality' => 85],
            'large' => ['width' => 1200, 'height' => 1200, 'quality' => 90],
        ];

        $sizes = array_merge($defaultSizes, $sizes);

        foreach ($sizes as $sizeName => $config) {
            try {
                $pathInfo = pathinfo($imagePath);
                $optimizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $sizeName . '.webp';
                $fullOptimizedPath = storage_path('app/public/' . $optimizedPath);

                // Create optimized version if it doesn't exist
                if (!file_exists($fullOptimizedPath)) {
                    $image = Image::make($originalPath);
                    
                    // Resize maintaining aspect ratio
                    $image->resize($config['width'], $config['height'], function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });

                    // Convert to WebP for better compression
                    $image->encode('webp', $config['quality']);
                    $image->save($fullOptimizedPath);
                }

                $optimizedImages[$sizeName] = [
                    'path' => $optimizedPath,
                    'url' => asset('storage/' . $optimizedPath),
                    'width' => $config['width'],
                    'height' => $config['height']
                ];
            } catch (\Exception $e) {
                // Log error but continue with other sizes
                \Log::error("Image optimization failed for {$imagePath}: " . $e->getMessage());
            }
        }

        return $optimizedImages;
    }

    /**
     * Generate responsive image srcset
     */
    public function generateSrcSet(array $optimizedImages): string
    {
        $srcset = [];
        
        foreach ($optimizedImages as $size => $image) {
            $srcset[] = $image['url'] . ' ' . $image['width'] . 'w';
        }

        return implode(', ', $srcset);
    }

    /**
     * Cache expensive database queries
     */
    public function cacheQuery(string $key, callable $callback, int $minutes = 60)
    {
        return Cache::remember($key, now()->addMinutes($minutes), $callback);
    }

    /**
     * Preload critical resources
     */
    public function getCriticalResources(): array
    {
        return [
            // Critical CSS
            [
                'rel' => 'preload',
                'href' => asset('css/app.css'),
                'as' => 'style'
            ],
            // Critical fonts
            [
                'rel' => 'preload',
                'href' => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
                'as' => 'style'
            ],
            // Critical JavaScript
            [
                'rel' => 'preload',
                'href' => asset('js/app.js'),
                'as' => 'script'
            ]
        ];
    }

    /**
     * Generate DNS prefetch hints
     */
    public function getDnsPrefetchHints(): array
    {
        return [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://www.google-analytics.com',
            'https://www.googletagmanager.com',
            'https://connect.facebook.net',
            'https://platform.twitter.com'
        ];
    }

    /**
     * Generate preconnect hints
     */
    public function getPreconnectHints(): array
    {
        return [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://www.google-analytics.com'
        ];
    }

    /**
     * Minify HTML output
     */
    public function minifyHtml(string $html): string
    {
        // Remove comments
        $html = preg_replace('/<!--(?!<!)[^\[>].*?-->/s', '', $html);
        
        // Remove extra whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        
        // Remove whitespace around tags
        $html = preg_replace('/>\s+</', '><', $html);
        
        return trim($html);
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public function getCriticalCss(): string
    {
        return '
        /* Critical CSS for above-the-fold content */
        body { font-family: Inter, sans-serif; margin: 0; padding: 0; }
        .header { background: #fff; border-bottom: 1px solid #e5e7eb; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
        .btn-primary { background: #ec4899; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; }
        .grid { display: grid; gap: 1.5rem; }
        @media (min-width: 768px) { .grid { grid-template-columns: repeat(2, 1fr); } }
        @media (min-width: 1024px) { .grid { grid-template-columns: repeat(3, 1fr); } }
        ';
    }

    /**
     * Get performance metrics configuration
     */
    public function getPerformanceConfig(): array
    {
        return [
            'cache_duration' => [
                'escorts' => 30, // 30 minutes
                'agencies' => 60, // 1 hour
                'locations' => 120, // 2 hours
                'static_content' => 1440, // 24 hours
            ],
            'image_optimization' => [
                'enable_webp' => true,
                'enable_lazy_loading' => true,
                'quality' => 85,
                'progressive_jpeg' => true,
            ],
            'compression' => [
                'enable_gzip' => true,
                'enable_brotli' => true,
                'min_file_size' => 1024, // 1KB
            ],
            'cdn' => [
                'enable' => false, // Set to true when CDN is configured
                'base_url' => env('CDN_URL', ''),
                'static_assets' => true,
                'images' => true,
            ]
        ];
    }

    /**
     * Clear performance caches
     */
    public function clearPerformanceCaches(): void
    {
        // Clear application cache
        Cache::flush();
        
        // Clear view cache
        \Artisan::call('view:clear');
        
        // Clear route cache
        \Artisan::call('route:clear');
        
        // Clear config cache
        \Artisan::call('config:clear');
    }

    /**
     * Generate service worker for offline functionality
     */
    public function generateServiceWorker(): string
    {
        return "
const CACHE_NAME = 'ghb-v1';
const urlsToCache = [
    '/',
    '/css/app.css',
    '/js/app.js',
    '/escorts',
    '/agencies',
    '/locations'
];

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
        ";
    }
}
