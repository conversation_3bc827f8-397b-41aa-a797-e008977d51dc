<?php

namespace App\Services;

use App\Models\Service;
use App\Models\Location;
use Illuminate\Support\Str;

class ServiceContentService
{
    /**
     * Get all service content categories and pages
     */
    public function getServiceContentStructure(): array
    {
        return [
            'companion_services' => [
                'title' => 'Companion Services',
                'description' => 'Professional companionship for various occasions',
                'services' => [
                    'dinner-companions' => 'Dinner Companions',
                    'social-events' => 'Social Event Companions',
                    'business-meetings' => 'Business Meeting Companions',
                    'travel-companions' => 'Travel Companions',
                    'cultural-events' => 'Cultural Event Companions',
                    'party-companions' => 'Party Companions',
                    'wedding-companions' => 'Wedding Event Companions',
                    'corporate-events' => 'Corporate Event Companions',
                    'conference-companions' => 'Conference Companions',
                    'gala-companions' => 'Gala Event Companions',
                ],
            ],
            'entertainment_services' => [
                'title' => 'Entertainment Services',
                'description' => 'Professional entertainment and social services',
                'services' => [
                    'nightlife-companions' => 'Nightlife Companions',
                    'casino-companions' => 'Casino Companions',
                    'theater-companions' => 'Theater Companions',
                    'concert-companions' => 'Concert Companions',
                    'sports-event-companions' => 'Sports Event Companions',
                    'festival-companions' => 'Festival Companions',
                    'art-gallery-companions' => 'Art Gallery Companions',
                    'museum-companions' => 'Museum Companions',
                    'wine-tasting-companions' => 'Wine Tasting Companions',
                    'shopping-companions' => 'Shopping Companions',
                ],
            ],
            'travel_services' => [
                'title' => 'Travel Services',
                'description' => 'Professional travel companionship services',
                'services' => [
                    'international-travel' => 'International Travel Companions',
                    'domestic-travel' => 'Domestic Travel Companions',
                    'business-travel' => 'Business Travel Companions',
                    'vacation-companions' => 'Vacation Companions',
                    'safari-companions' => 'Safari Companions',
                    'city-tours' => 'City Tour Companions',
                    'weekend-getaways' => 'Weekend Getaway Companions',
                    'luxury-travel' => 'Luxury Travel Companions',
                    'adventure-travel' => 'Adventure Travel Companions',
                    'cultural-tours' => 'Cultural Tour Companions',
                ],
            ],
            'professional_services' => [
                'title' => 'Professional Services',
                'description' => 'Professional and business-oriented services',
                'services' => [
                    'personal-assistant' => 'Personal Assistant Services',
                    'translation-services' => 'Translation Services',
                    'cultural-guidance' => 'Cultural Guidance',
                    'business-networking' => 'Business Networking',
                    'professional-coaching' => 'Professional Coaching',
                    'etiquette-training' => 'Etiquette Training',
                    'language-practice' => 'Language Practice',
                    'presentation-assistance' => 'Presentation Assistance',
                    'client-entertainment' => 'Client Entertainment',
                    'hospitality-services' => 'Hospitality Services',
                ],
            ],
            'specialized_services' => [
                'title' => 'Specialized Services',
                'description' => 'Unique and specialized companion services',
                'services' => [
                    'fitness-companions' => 'Fitness Companions',
                    'wellness-companions' => 'Wellness Companions',
                    'photography-models' => 'Photography Models',
                    'fashion-companions' => 'Fashion Event Companions',
                    'charity-events' => 'Charity Event Companions',
                    'religious-events' => 'Religious Event Companions',
                    'graduation-companions' => 'Graduation Companions',
                    'anniversary-companions' => 'Anniversary Companions',
                    'birthday-companions' => 'Birthday Companions',
                    'holiday-companions' => 'Holiday Companions',
                ],
            ],
        ];
    }

    /**
     * Generate service-location combination pages
     */
    public function getServiceLocationCombinations(): array
    {
        $majorCities = [
            'kampala' => 'Kampala',
            'entebbe' => 'Entebbe', 
            'jinja' => 'Jinja',
            'mbarara' => 'Mbarara',
            'gulu' => 'Gulu',
            'lira' => 'Lira',
            'mbale' => 'Mbale',
            'fort-portal' => 'Fort Portal',
        ];

        $topServices = [
            'dinner-companions' => 'Dinner Companions',
            'travel-companions' => 'Travel Companions',
            'business-meetings' => 'Business Meeting Companions',
            'social-events' => 'Social Event Companions',
            'nightlife-companions' => 'Nightlife Companions',
        ];

        $combinations = [];
        
        foreach ($topServices as $serviceSlug => $serviceName) {
            foreach ($majorCities as $citySlug => $cityName) {
                $combinations[] = [
                    'service_slug' => $serviceSlug,
                    'service_name' => $serviceName,
                    'city_slug' => $citySlug,
                    'city_name' => $cityName,
                    'url' => "/services/{$serviceSlug}/{$citySlug}",
                    'title' => "{$serviceName} in {$cityName}, Uganda",
                    'priority' => $this->getServiceLocationPriority($serviceSlug, $citySlug),
                ];
            }
        }

        return $combinations;
    }

    /**
     * Generate content for a specific service page
     */
    public function generateServicePageContent(string $serviceSlug, string $citySlug = null): array
    {
        $serviceData = $this->getServiceData($serviceSlug);
        $cityData = $citySlug ? $this->getCityData($citySlug) : null;

        if ($citySlug) {
            return $this->generateServiceLocationContent($serviceData, $cityData);
        }

        return $this->generateServiceContent($serviceData);
    }

    /**
     * Generate service-specific content
     */
    private function generateServiceContent(array $serviceData): array
    {
        return [
            'title' => $serviceData['name'] . ' in Uganda | Professional Escort Services',
            'meta_description' => "Find professional {$serviceData['name']} in Uganda. Verified escorts providing {$serviceData['description']} across major cities.",
            'content' => [
                'hero' => [
                    'title' => "Professional {$serviceData['name']} in Uganda",
                    'subtitle' => "Verified escorts providing {$serviceData['description']}",
                    'description' => "Discover professional {$serviceData['name']} with our verified escort directory. Safe, discreet, and professional services across Uganda.",
                ],
                'overview' => [
                    'title' => "About {$serviceData['name']}",
                    'content' => $serviceData['detailed_description'],
                ],
                'what_to_expect' => [
                    'title' => "What to Expect",
                    'expectations' => $serviceData['expectations'],
                ],
                'booking_process' => [
                    'title' => "How to Book {$serviceData['name']}",
                    'steps' => [
                        'Browse verified escorts offering this service',
                        'Review profiles and rates',
                        'Contact through secure messaging',
                        'Discuss requirements and arrangements',
                        'Confirm booking and payment',
                    ],
                ],
                'pricing' => [
                    'title' => "Pricing Information",
                    'content' => $serviceData['pricing_info'],
                ],
                'safety' => [
                    'title' => "Safety Guidelines",
                    'guidelines' => $serviceData['safety_guidelines'],
                ],
            ],
            'related_services' => $this->getRelatedServices($serviceData['slug']),
            'available_cities' => $this->getAvailableCities(),
        ];
    }

    /**
     * Generate service + location specific content
     */
    private function generateServiceLocationContent(array $serviceData, array $cityData): array
    {
        return [
            'title' => "{$serviceData['name']} in {$cityData['name']}, Uganda | Verified Escorts",
            'meta_description' => "Find professional {$serviceData['name']} in {$cityData['name']}, Uganda. Verified local escorts providing {$serviceData['description']}.",
            'content' => [
                'hero' => [
                    'title' => "{$serviceData['name']} in {$cityData['name']}",
                    'subtitle' => "Professional companions in {$cityData['name']}, Uganda",
                    'description' => "Find verified {$serviceData['name']} in {$cityData['name']}. Our local escorts provide professional {$serviceData['description']} with discretion and professionalism.",
                ],
                'local_overview' => [
                    'title' => "{$serviceData['name']} Scene in {$cityData['name']}",
                    'content' => "The {$serviceData['name']} scene in {$cityData['name']} offers {$cityData['service_description']} with professional escorts who understand local culture and preferences.",
                ],
                'popular_venues' => [
                    'title' => "Popular Venues in {$cityData['name']}",
                    'venues' => $cityData['popular_venues'],
                ],
                'local_rates' => [
                    'title' => "Rates in {$cityData['name']}",
                    'content' => $cityData['rate_info'],
                ],
                'booking_tips' => [
                    'title' => "Booking Tips for {$cityData['name']}",
                    'tips' => $cityData['booking_tips'],
                ],
                'safety_considerations' => [
                    'title' => "Safety in {$cityData['name']}",
                    'content' => $cityData['safety_info'],
                ],
            ],
            'local_escorts' => $this->getLocalEscorts($serviceData['slug'], $cityData['slug']),
            'related_locations' => $this->getRelatedLocations($cityData['slug']),
        ];
    }

    /**
     * Get service data by slug
     */
    private function getServiceData(string $serviceSlug): array
    {
        $serviceMap = [
            'dinner-companions' => [
                'slug' => 'dinner-companions',
                'name' => 'Dinner Companions',
                'description' => 'elegant dining experiences and social companionship',
                'detailed_description' => 'Professional dinner companions provide sophisticated companionship for dining experiences, business dinners, and social events. Our verified escorts are well-educated, articulate, and experienced in fine dining etiquette.',
                'expectations' => [
                    'Sophisticated conversation and companionship',
                    'Appropriate attire for the venue',
                    'Knowledge of dining etiquette',
                    'Professional and discreet behavior',
                    'Punctuality and reliability',
                ],
                'pricing_info' => 'Dinner companion rates typically range from UGX 300,000 to UGX 800,000 depending on the venue, duration, and escort experience.',
                'safety_guidelines' => [
                    'Meet at the restaurant or public venue',
                    'Verify escort identity before meeting',
                    'Choose reputable dining establishments',
                    'Discuss expectations clearly beforehand',
                ],
            ],
            'travel-companions' => [
                'slug' => 'travel-companions',
                'name' => 'Travel Companions',
                'description' => 'professional companionship for business and leisure travel',
                'detailed_description' => 'Travel companions provide professional companionship for business trips, vacations, and travel experiences. Our escorts are experienced travelers who can enhance your journey with local knowledge and engaging companionship.',
                'expectations' => [
                    'Professional travel companionship',
                    'Local knowledge and cultural insights',
                    'Flexibility with travel schedules',
                    'Appropriate documentation for travel',
                    'Professional appearance and behavior',
                ],
                'pricing_info' => 'Travel companion rates vary based on destination, duration, and travel requirements. Daily rates typically start from UGX 1,000,000.',
                'safety_guidelines' => [
                    'Verify all travel documentation',
                    'Use reputable accommodation',
                    'Maintain emergency contacts',
                    'Follow local laws and customs',
                ],
            ],
            // Add more service definitions as needed
        ];

        return $serviceMap[$serviceSlug] ?? $this->getDefaultServiceData($serviceSlug);
    }

    /**
     * Get city data by slug
     */
    private function getCityData(string $citySlug): array
    {
        $cityMap = [
            'kampala' => [
                'slug' => 'kampala',
                'name' => 'Kampala',
                'service_description' => 'diverse dining and entertainment options',
                'popular_venues' => [
                    'Serena Hotel Kampala',
                    'Sheraton Kampala Hotel',
                    'Protea Hotel by Marriott',
                    'The Lawns Restaurant',
                    'Mediterraneo Restaurant',
                ],
                'rate_info' => 'Kampala rates are typically 20-30% higher than other cities due to premium venues and higher demand.',
                'booking_tips' => [
                    'Book in advance for popular venues',
                    'Consider traffic when planning timing',
                    'Choose central locations for convenience',
                ],
                'safety_info' => 'Kampala offers excellent safety with many upscale venues in secure areas like Kololo and Nakasero.',
            ],
            'entebbe' => [
                'slug' => 'entebbe',
                'name' => 'Entebbe',
                'service_description' => 'lakeside dining and airport proximity',
                'popular_venues' => [
                    'Lake Victoria Serena Golf Resort',
                    'Protea Hotel Entebbe',
                    'Anderita Beach Hotel',
                    'The Boma Hotel',
                ],
                'rate_info' => 'Entebbe rates are competitive with additional travel time considerations for airport proximity.',
                'booking_tips' => [
                    'Consider flight schedules for timing',
                    'Book lakeside venues for scenic experiences',
                    'Factor in travel time from Kampala',
                ],
                'safety_info' => 'Entebbe is very safe with good security around hotels and the airport area.',
            ],
        ];

        return $cityMap[$citySlug] ?? $this->getDefaultCityData($citySlug);
    }

    /**
     * Get default service data
     */
    private function getDefaultServiceData(string $serviceSlug): array
    {
        $serviceName = ucwords(str_replace('-', ' ', $serviceSlug));
        
        return [
            'slug' => $serviceSlug,
            'name' => $serviceName,
            'description' => 'professional companion services',
            'detailed_description' => "Professional {$serviceName} provide high-quality companionship services with verified escorts who maintain the highest standards of professionalism.",
            'expectations' => [
                'Professional and discreet service',
                'Punctuality and reliability',
                'Appropriate attire and behavior',
                'Respectful communication',
            ],
            'pricing_info' => 'Rates vary based on duration, location, and specific requirements. Contact escorts directly for detailed pricing.',
            'safety_guidelines' => [
                'Always verify escort identity',
                'Meet in safe, public locations',
                'Communicate expectations clearly',
                'Follow platform safety guidelines',
            ],
        ];
    }

    /**
     * Get default city data
     */
    private function getDefaultCityData(string $citySlug): array
    {
        $cityName = ucwords(str_replace('-', ' ', $citySlug));
        
        return [
            'slug' => $citySlug,
            'name' => $cityName,
            'service_description' => 'local entertainment and dining options',
            'popular_venues' => [
                'Local hotels and restaurants',
                'Entertainment venues',
                'Cultural sites',
            ],
            'rate_info' => "Rates in {$cityName} are competitive and vary based on specific services and duration.",
            'booking_tips' => [
                'Research local venues in advance',
                'Consider local customs and preferences',
                'Book verified escorts through our platform',
            ],
            'safety_info' => "{$cityName} offers good safety standards with proper precautions and verified escorts.",
        ];
    }

    /**
     * Helper methods
     */
    private function getServiceLocationPriority(string $serviceSlug, string $citySlug): string
    {
        $highPriorityServices = ['dinner-companions', 'travel-companions', 'business-meetings'];
        $highPriorityCities = ['kampala', 'entebbe', 'jinja'];

        if (in_array($serviceSlug, $highPriorityServices) && in_array($citySlug, $highPriorityCities)) {
            return 'high';
        }

        return 'medium';
    }

    private function getRelatedServices(string $currentService): array
    {
        // Return related services based on current service
        return [];
    }

    private function getAvailableCities(): array
    {
        return ['Kampala', 'Entebbe', 'Jinja', 'Mbarara', 'Gulu'];
    }

    private function getLocalEscorts(string $serviceSlug, string $citySlug): array
    {
        // Return escorts offering this service in this city
        return [];
    }

    private function getRelatedLocations(string $currentCity): array
    {
        // Return related cities
        return [];
    }

    /**
     * Generate content calendar for service pages
     */
    public function getServiceContentCalendar(): array
    {
        return [
            'week_9' => [
                'companion-services/dinner-companions',
                'companion-services/social-events', 
                'companion-services/business-meetings',
                'companion-services/travel-companions',
                'companion-services/cultural-events',
                'entertainment-services/nightlife-companions',
                'entertainment-services/casino-companions',
                'entertainment-services/theater-companions',
                'entertainment-services/concert-companions',
                'entertainment-services/sports-event-companions',
            ],
            'week_10' => [
                'travel-services/international-travel',
                'travel-services/domestic-travel',
                'travel-services/business-travel',
                'travel-services/vacation-companions',
                'travel-services/safari-companions',
                'professional-services/personal-assistant',
                'professional-services/translation-services',
                'professional-services/cultural-guidance',
                'professional-services/business-networking',
                'professional-services/professional-coaching',
            ],
            'week_11' => [
                'specialized-services/fitness-companions',
                'specialized-services/wellness-companions',
                'specialized-services/photography-models',
                'specialized-services/fashion-companions',
                'specialized-services/charity-events',
                // Service + Location combinations (high priority)
                'dinner-companions/kampala',
                'dinner-companions/entebbe',
                'travel-companions/kampala',
                'business-meetings/kampala',
                'nightlife-companions/kampala',
            ],
            'week_12' => [
                // More service + location combinations
                'dinner-companions/jinja',
                'dinner-companions/mbarara',
                'travel-companions/entebbe',
                'travel-companions/jinja',
                'social-events/kampala',
                'social-events/entebbe',
                'business-meetings/entebbe',
                'business-meetings/jinja',
                'nightlife-companions/entebbe',
                'nightlife-companions/jinja',
            ],
        ];
    }
}
