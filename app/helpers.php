<?php

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;

if (!function_exists('setting')) {
    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        // Check if the settings table exists
        if (!Schema::hasTable('settings')) {
            return $default;
        }

        // Try to get from cache first
        if (Cache::has('setting_' . $key)) {
            return Cache::get('setting_' . $key);
        }

        // Get from database
        $setting = Setting::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Cache the result for future use
        Cache::put('setting_' . $key, $setting->value, now()->addHours(24));

        return $setting->value;
    }
}
