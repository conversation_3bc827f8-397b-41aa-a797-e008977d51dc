<?php

// This script downloads sample images from Unsplash for the escorts

// Sample image URLs for each gender
$femaleImages = [
    'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=600&q=80',
    'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=600&q=80',
    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=600&q=80',
    'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=600&q=80',
    'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=600&q=80',
];

$maleImages = [
    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=600&q=80',
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=600&q=80',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&q=80',
    'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=600&q=80',
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=600&q=80',
];

$transImages = [
    'https://images.unsplash.com/photo-1596075780750-81249df16d19?w=600&q=80',
    'https://images.unsplash.com/photo-1611432579699-484f7990b127?w=600&q=80',
    'https://images.unsplash.com/photo-1596747742222-008b5a0e3125?w=600&q=80',
    'https://images.unsplash.com/photo-1604072366595-e75dc92d6bdc?w=600&q=80',
    'https://images.unsplash.com/photo-1613876215075-276638e9a68c?w=600&q=80',
];

$coupleImages = [
    'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=600&q=80',
    'https://images.unsplash.com/photo-1537261131936-3cdff36a1ac9?w=600&q=80',
    'https://images.unsplash.com/photo-1518199266791-5375a83190b7?w=600&q=80',
    'https://images.unsplash.com/photo-1539150590121-7bb5808b1e8e?w=600&q=80',
    'https://images.unsplash.com/photo-1522098543979-ffc5003f2089?w=600&q=80',
];

// Create directories if they don't exist
$directories = [
    'public/storage/escorts/female',
    'public/storage/escorts/male',
    'public/storage/escorts/transsexual',
    'public/storage/escorts/couple',
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Download female images
echo "Downloading female images...\n";
foreach ($femaleImages as $index => $url) {
    $filename = "public/storage/escorts/female/female-" . ($index + 1) . ".jpg";
    file_put_contents($filename, file_get_contents($url));
    echo "Downloaded: $filename\n";
}

// Download male images
echo "Downloading male images...\n";
foreach ($maleImages as $index => $url) {
    $filename = "public/storage/escorts/male/male-" . ($index + 1) . ".jpg";
    file_put_contents($filename, file_get_contents($url));
    echo "Downloaded: $filename\n";
}

// Download trans images
echo "Downloading trans images...\n";
foreach ($transImages as $index => $url) {
    $filename = "public/storage/escorts/transsexual/trans-" . ($index + 1) . ".jpg";
    file_put_contents($filename, file_get_contents($url));
    echo "Downloaded: $filename\n";
}

// Download couple images
echo "Downloading couple images...\n";
foreach ($coupleImages as $index => $url) {
    $filename = "public/storage/escorts/couple/couple-" . ($index + 1) . ".jpg";
    file_put_contents($filename, file_get_contents($url));
    echo "Downloaded: $filename\n";
}

echo "All images downloaded successfully!\n";
